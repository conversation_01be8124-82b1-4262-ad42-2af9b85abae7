version: '3.8'

services:
  # Development PostgreSQL Database
  postgres-dev:
    image: postgres:15-alpine
    container_name: planeacion-nem-db-dev
    environment:
      POSTGRES_DB: planeacion_nem_dev
      POSTGRES_USER: nem_dev
      POSTGRES_PASSWORD: nem_dev_password
    ports:
      - "5434:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./server/db/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U nem_dev -d planeacion_nem_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Development Application with hot reload
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: planeacion-nem-app-dev
    environment:
      NODE_ENV: development
      DATABASE_URL: *******************************************************/planeacion_nem_dev
      PORT: 3001
      SESSION_SECRET: dev-secret-key-change-in-production
      GEMINI_API_KEY: ${GEMINI_API_KEY:-}
    ports:
      - "3001:3001"
      - "5173:5173"  # Vite dev server
    depends_on:
      postgres-dev:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/node_modules
      - ./uploads:/app/uploads
    command: npm run dev
    restart: unless-stopped

volumes:
  postgres_dev_data:

networks:
  default:
    name: planeacion-nem-dev-network