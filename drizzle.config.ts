import { defineConfig } from "drizzle-kit";

export default defineConfig({
  schema: "./server/db/schema.ts",
  out: "./server/db/migrations",
  dialect: "postgresql",
  dbCredentials: {
    host: "localhost",
    port: parseInt(process.env.DB_PORT || "5434"),
    database: process.env.DB_NAME || "planeacion_nem",
    user: process.env.DB_USER || "postgres",
    password: process.env.DB_PASSWORD || "postgres",
  },
  verbose: true,
  strict: true,
});
