# 🐳 Dockerización de Planeación NEM

## 📋 Descripción

Este proyecto ha sido completamente dockerizado para facilitar el desarrollo, testing y despliegue. Incluye configuración para PostgreSQL, desarrollo con hot reload, y producción optimizada.

## 🚀 Inicio Rápido

### **Opción 1: Desarrollo con Docker Compose**
```bash
# Iniciar todo el entorno de desarrollo
docker-compose -f docker-compose.dev.yml up -d

# Ver logs
docker-compose -f docker-compose.dev.yml logs -f

# Detener
docker-compose -f docker-compose.dev.yml down
```

### **Opción 2: Producción**
```bash
# Iniciar en modo producción
docker-compose up -d

# Ver logs
docker-compose logs -f

# Detener
docker-compose down
```

## 📦 Servicios Disponibles

| Servicio | Puerto | Descripción |
|----------|--------|-------------|
| **app** | 3001 | Aplicación principal |
| **postgres** | 5433 | Base de datos PostgreSQL |
| **app-dev** | 3001/5173 | Desarrollo con hot reload |
| **postgres-dev** | 5434 | Base de datos para desarrollo |

## 🔧 Comandos Útiles

### **Gestión de Contenedores**
```bash
# Ver contenedores activos
docker ps

# Ver logs de un servicio específico
docker-compose logs app
docker-compose logs postgres

# Ejecutar comandos dentro del contenedor
docker-compose exec app npm run db:migrate:sep
docker-compose exec postgres psql -U nem_user -d planeacion_nem

# Acceder al contenedor
docker-compose exec app sh
```

### **Migraciones y Datos**
```bash
# Ejecutar migraciones
docker-compose exec app npm run db:migrate

# Ejecutar migración SEP
docker-compose exec app npm run db:migrate:sep

# Acceder a la base de datos
docker-compose exec postgres psql -U nem_user -d planeacion_nem
```

### **Desarrollo**
```bash
# Iniciar en modo desarrollo
docker-compose -f docker-compose.dev.yml up -d

# Ver cambios en tiempo real
docker-compose -f docker-compose.dev.yml logs -f app-dev

# Reconstruir después de cambios
docker-compose -f docker-compose.dev.yml build
docker-compose -f docker-compose.dev.yml up -d
```

## 🏗️ Estructura de Archivos Docker

```
├── Dockerfile              # Producción optimizada
├── Dockerfile.dev          # Desarrollo con hot reload
├── docker-compose.yml      # Producción completa
├── docker-compose.dev.yml  # Desarrollo con PostgreSQL
├── .dockerignore          # Archivos a ignorar
└── server/db/init/        # Scripts de inicialización
    └── 01-init.sql        # Configuración PostgreSQL
```

## ⚙️ Variables de Entorno

### **Archivo .env para desarrollo**
```bash
# Database
DATABASE_URL=postgresql://nem_dev:nem_dev_password@localhost:5434/planeacion_nem_dev

# API Keys
GEMINI_API_KEY=your-gemini-api-key

# Server
PORT=3001
NODE_ENV=development
SESSION_SECRET=your-session-secret
```

### **Archivo .env para producción**
```bash
# Database
DATABASE_URL=************************************************/planeacion_nem

# API Keys
GEMINI_API_KEY=your-gemini-api-key

# Server
PORT=3001
NODE_ENV=production
SESSION_SECRET=your-production-secret
```

## 🔄 Flujo de Desarrollo

### **1. Primera vez**
```bash
# Clonar repositorio
git clone [repository-url]
cd planeacion-nem

# Copiar variables de entorno
cp .env.example .env

# Iniciar entorno de desarrollo
docker-compose -f docker-compose.dev.yml up -d

# Ejecutar migraciones
docker-compose -f docker-compose.dev.yml exec app-dev npm run db:migrate:sep
```

### **2. Desarrollo diario**
```bash
# Iniciar entorno
docker-compose -f docker-compose.dev.yml up -d

# Desarrollar con hot reload
# Los cambios se reflejan automáticamente
```

### **3. Testing**
```bash
# Ejecutar tests
docker-compose -f docker-compose.dev.yml exec app-dev npm test

# Coverage
docker-compose -f docker-compose.dev.yml exec app-dev npm run test:coverage
```

## 📊 Monitoreo

### **Health Checks**
- **PostgreSQL**: `http://localhost:5433/health`
- **Aplicación**: `http://localhost:3001/health`

### **Logs y Debugging**
```bash
# Ver todos los logs
docker-compose logs -f

# Ver logs de un servicio específico
docker-compose logs -f app

# Ver estadísticas
docker stats
```

## 🚀 Despliegue

### **Producción con Docker**
```bash
# Construir y ejecutar
docker-compose up -d

# Verificar estado
docker-compose ps
```

### **Despliegue en la nube**
El proyecto está listo para desplegar en:
- **AWS ECS**
- **Google Cloud Run**
- **Azure Container Instances**
- **DigitalOcean App Platform**

## 🔍 Solución de Problemas

### **Error: Puerto ya en uso**
```bash
# Verificar qué está usando el puerto
lsof -i :5433
lsof -i :3001

# Cambiar puertos en docker-compose.yml
```

### **Error: Permisos de Docker**
```bash
# Agregar usuario al grupo docker
sudo usermod -aG docker $USER
# Reiniciar sesión
```

### **Limpiar contenedores**
```bash
# Detener y eliminar todo
docker-compose down -v
docker system prune -a
```

## 📝 Notas Importantes

- **Persistencia de datos**: Los volúmenes Docker mantienen los datos entre reinicios
- **Hot reload**: En desarrollo, los cambios se reflejan automáticamente
- **Seguridad**: Los secretos deben gestionarse con variables de entorno
- **Performance**: Las imágenes están optimizadas para tamaño mínimo