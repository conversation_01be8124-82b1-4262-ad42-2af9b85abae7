version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: planeacion-nem-db
    environment:
      POSTGRES_DB: planeacion_nem
      POSTGRES_USER: nem_user
      POSTGRES_PASSWORD: nem_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/db/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U nem_user -d planeacion_nem"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Application Server
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: planeacion-nem-app
    environment:
      NODE_ENV: production
      DATABASE_URL: ************************************************/planeacion_nem
      PORT: 3001
      SESSION_SECRET: your-secret-key-here
      GEMINI_API_KEY: ${GEMINI_API_KEY:-}
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped

  # Development Database (optional)
  postgres-dev:
    image: postgres:15-alpine
    container_name: planeacion-nem-db-dev
    environment:
      POSTGRES_DB: planeacion_nem_dev
      POSTGRES_USER: nem_dev
      POSTGRES_PASSWORD: nem_dev_password
    ports:
      - "5434:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    profiles:
      - dev

volumes:
  postgres_data:
  postgres_dev_data:

networks:
  default:
    name: planeacion-nem-network