# Configuración de Gemini AI

## Requisitos Previos

1. **Obtener API Key de Gemini**
   - Ve a [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Inicia sesión con tu cuenta de Google
   - Crea una nueva API key
   - Copia la clave generada

2. **Configurar Variables de Entorno**

## Configuración Rápida

### Paso 1: Configurar la API Key
```bash
# Copia el archivo de ejemplo
cp .env.example .env

# Edita el archivo .env y agrega tu API key
GEMINI_API_KEY=tu-api-key-aqui
```

### Paso 2: Verificar la Configuración
El proyecto está configurado para usar Gemini por defecto en:
- **Backend**: `server/routes/ai.ts` - Usa `@google/generative-ai`
- **Modelos**: 
  - `gemini-1.5-flash` para chat general
  - `gemini-1.5-pro` para generación de contenido y validación

### Paso 3: Reiniciar los Servicios
```bash
# Si los servicios están corriendo, reinícialos
# Terminal 1 (Backend)
npm run server

# Terminal 2 (Frontend)
npm run dev
```

## Uso de la IA

### Funcionalidades Disponibles

1. **Chat con Asistente Pedagógico**
   - Ubicación: Panel lateral en el wizard de planeación
   - Uso: Haz preguntas sobre metodología NEM, PDAs, actividades, etc.

2. **Generación de Contenido**
   - Objetivos de aprendizaje
   - Actividades didácticas
   - Instrumentos de evaluación
   - Rúbricas y listas de cotejo

3. **Validación de Planeaciones**
   - Análisis de coherencia con NEM
   - Sugerencias de mejora
   - Evaluación de alineación con ejes articuladores

### Ejemplos de Uso

#### Chat Básico
```javascript
// En cualquier componente
import { aiApi } from '@/lib/api';

const response = await aiApi.chat(
  "¿Cómo integro el pensamiento crítico en una clase de matemáticas?",
  {
    grade: "2° Secundaria",
    subject: "Matemáticas",
    formativeField: "Saberes y Pensamiento Científico"
  }
);
```

#### Generar Contenido
```javascript
const content = await aiApi.generateContent(
  "actividades",
  "saberes-cientificos",
  {
    grade: "3° Secundaria",
    subject: "Ciencias Naturales",
    articulatingAxes: ["pensamiento-critico", "vida-saludable"]
  }
);
```

## Solución de Problemas

### Error: "API Key no válida"
1. Verifica que la API key esté correctamente copiada en `.env`
2. Asegúrate de que no haya espacios extra
3. Comprueba que la API key esté activa en Google AI Studio

### Error: "Límite de cuota excedido"
1. Verifica tu cuota en [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Considera usar `gemini-1.5-flash` en lugar de `gemini-1.5-pro` para reducir costos

### Error: "Network Error"
1. Verifica tu conexión a internet
2. Asegúrate de que el backend esté corriendo en `http://localhost:3002`
3. Revisa los logs del servidor: `npm run server`

## Configuración Avanzada

### Cambiar Modelos
Puedes modificar los modelos en `server/routes/ai.ts`:

```javascript
// Para chat general
const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

// Para generación compleja
const model = genAI.getGenerativeModel({ model: 'gemini-1.5-pro' });
```

### Ajustar Parámetros
En `server/routes/ai.ts`, puedes ajustar:
- `temperature`: Controla la creatividad (0.0 - 1.0)
- `maxOutputTokens`: Límite de tokens en la respuesta
- `topP` y `topK`: Controlan la diversidad de respuestas

## Notas Importantes

- **Gratuito**: Gemini ofrece una capa gratuita con límites generosos
- **Privacidad**: Las conversaciones se almacenan localmente en tu base de datos
- **Contexto**: El asistente está especializado en la Nueva Escuela Mexicana
- **Actualizaciones**: Google actualiza frecuentemente los modelos sin costo adicional