import axios from "axios";

const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:3001/api";

const api = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

// Demo mode - bypass actual API calls for testing
const DEMO_MODE = false;

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
}

interface Resource {
  id: string;
  title: string;
  type:
    | "pda"
    | "activity"
    | "project"
    | "rubric"
    | "document"
    | "video"
    | "image";
  category: string;
  grade?: string;
  subject?: string;
  content: Record<string, unknown>;
  tags: string[];
  isOfficial: boolean;
  downloads: number;
  rating: number;
  averageRating: number;
  ratingCount: number;
  createdAt: string;
  author?: string;
}

interface ResourceUploadData {
  title: string;
  type: string;
  category: string;
  grade?: string;
  subject?: string;
  content: Record<string, unknown>;
  tags?: string[];
}

interface PlanningData {
  id?: string;
  title?: string;
  description?: string;
  grade?: string;
  subject?: string;
  duration?: string;
  objectives?: string[];
  content?: Record<string, unknown>;
  [key: string]: unknown;
}

interface AIContext {
  grade?: string;
  subject?: string;
  topic?: string;
  objectives?: string[];
  [key: string]: unknown;
}

// Demo user data
const DEMO_USER: User = {
  id: "demo-user-123",
  email: "<EMAIL>",
  firstName: "Profesor",
  lastName: "Demo",
  role: "teacher",
};

// Demo resources data
const DEMO_RESOURCES: Resource[] = [
  {
    id: "1",
    title: "PDA: Participa en intercambios orales",
    type: "pda",
    category: "Lenguajes",
    grade: "1° Secundaria",
    subject: "Español",
    content: { description: "Desarrollo de habilidades orales" },
    tags: ["oralidad", "comunicación", "expresión"],
    isOfficial: true,
    downloads: 1250,
    rating: 4.8,
    averageRating: 4.8,
    ratingCount: 89,
    createdAt: "2024-01-15T10:00:00Z",
    author: "SEP",
  },
  {
    id: "2",
    title: "Actividad: Debate sobre Cambio Climático",
    type: "activity",
    category: "Saberes y Pensamiento Científico",
    grade: "2° Secundaria",
    subject: "Ciencias Naturales",
    content: { description: "Actividad práctica sobre cambio climático" },
    tags: ["ciencia", "ambiente", "debate"],
    isOfficial: false,
    downloads: 342,
    rating: 4.5,
    averageRating: 4.5,
    ratingCount: 28,
    createdAt: "2024-01-20T15:30:00Z",
    author: "Comunidad Educativa",
  },
  {
    id: "3",
    title: "Proyecto: Huerto Escolar Sustentable",
    type: "project",
    category: "Ética, Naturaleza y Sociedades",
    grade: "3° Secundaria",
    subject: "Ciencias Naturales",
    content: { description: "Guía para crear un huerto escolar" },
    tags: ["sustentabilidad", "proyecto", "naturaleza"],
    isOfficial: true,
    downloads: 567,
    rating: 4.7,
    averageRating: 4.7,
    ratingCount: 45,
    createdAt: "2024-01-25T09:15:00Z",
    author: "SEP",
  },
];

// Demo API responses
const createDemoResponse = <T>(data: T) => ({
  data,
  status: 200,
  statusText: "OK",
  headers: {},
  config: {},
});

// Resources API with demo mode
export const resourcesApi = {
  getResources: (params?: Record<string, string>) => {
    if (DEMO_MODE) {
      let filtered = DEMO_RESOURCES;

      if (params?.type && params.type !== "all") {
        filtered = filtered.filter((r) => r.type === params.type);
      }
      if (params?.category && params.category !== "Todos") {
        filtered = filtered.filter((r) => r.category === params.category);
      }
      if (params?.grade && params.grade !== "Todos") {
        filtered = filtered.filter((r) => r.grade === params.grade);
      }
      if (params?.subject && params.subject !== "Todas") {
        filtered = filtered.filter((r) => r.subject === params.subject);
      }
      if (params?.search) {
        const search = params.search.toLowerCase();
        filtered = filtered.filter(
          (r) =>
            r.title.toLowerCase().includes(search) ||
            r.tags.some((tag) => tag.toLowerCase().includes(search))
        );
      }

      return Promise.resolve(createDemoResponse({ resources: filtered }));
    }
    return api.get("/resources", { params });
  },

  getResource: (id: string) => {
    if (DEMO_MODE) {
      const resource = DEMO_RESOURCES.find((r) => r.id === id);
      return Promise.resolve(createDemoResponse({ resource }));
    }
    return api.get(`/resources/${id}`);
  },

  rateResource: (id: string, rating: number) => {
    if (DEMO_MODE) {
      return Promise.resolve(createDemoResponse({ success: true }));
    }
    return api.post(`/resources/${id}/rate`, { rating });
  },

  toggleFavorite: (id: string) => {
    if (DEMO_MODE) {
      return Promise.resolve(createDemoResponse({ success: true }));
    }
    return api.post(`/resources/${id}/favorite`);
  },

  getFavorites: () => {
    if (DEMO_MODE) {
      return Promise.resolve(
        createDemoResponse({ resources: [DEMO_RESOURCES[0]] })
      );
    }
    return api.get("/resources/favorites/me");
  },

  trackDownload: (id: string) => {
    if (DEMO_MODE) {
      return Promise.resolve(createDemoResponse({ success: true }));
    }
    return api.post(`/resources/${id}/download`);
  },

  getFeatured: () => {
    if (DEMO_MODE) {
      return Promise.resolve(
        createDemoResponse({ resources: DEMO_RESOURCES.slice(0, 3) })
      );
    }
    return api.get("/resources/featured/popular");
  },

  uploadResource: (data: ResourceUploadData) => {
    if (DEMO_MODE) {
      return Promise.resolve(createDemoResponse({ success: true }));
    }
    return api.post("/resources/upload", data);
  },
};

// Auth API with demo mode
export const authApi = {
  login: (email: string, password: string) => {
    if (DEMO_MODE) {
      if (email === "<EMAIL>" && password === "demo123") {
        localStorage.setItem("authToken", "demo-token-123");
        return Promise.resolve(
          createDemoResponse({
            success: true,
            user: DEMO_USER,
            token: "demo-token-123",
          })
        );
      }
      return Promise.reject(new Error("Credenciales inválidas"));
    }
    return api.post("/auth/login", { email, password });
  },

  register: (userData: Record<string, unknown>) => {
    if (DEMO_MODE) {
      return Promise.resolve(
        createDemoResponse({
          success: true,
          user: DEMO_USER,
          token: "demo-token-123",
        })
      );
    }
    return api.post("/auth/register", userData);
  },

  logout: () => {
    if (DEMO_MODE) {
      localStorage.removeItem("authToken");
      return Promise.resolve(createDemoResponse({ success: true }));
    }
    return api.post("/auth/logout");
  },

  getCurrentUser: () => {
    if (DEMO_MODE) {
      const token = localStorage.getItem("authToken");
      if (token === "demo-token-123") {
        return Promise.resolve(createDemoResponse({ user: DEMO_USER }));
      }
      return Promise.reject(new Error("No autenticado"));
    }
    return api.get("/auth/me");
  },

  getUser: () => {
    if (DEMO_MODE) {
      const token = localStorage.getItem("authToken");
      if (token === "demo-token-123") {
        return Promise.resolve(createDemoResponse({ user: DEMO_USER }));
      }
      return Promise.reject(new Error("No autenticado"));
    }
    return api.get("/auth/me");
  },
};

// Planning API with demo mode
export const planningApi = {
  getPlannings: () => {
    if (DEMO_MODE) {
      return Promise.resolve(createDemoResponse({ plannings: [] }));
    }
    return api.get("/plannings");
  },
  getPlanning: (id: string) => {
    if (DEMO_MODE) {
      return Promise.resolve(createDemoResponse({ planning: null }));
    }
    return api.get(`/plannings/${id}`);
  },
  createPlanning: (data: PlanningData) => {
    if (DEMO_MODE) {
      return Promise.resolve(createDemoResponse({ success: true }));
    }
    return api.post("/plannings", data);
  },
  updatePlanning: (id: string, data: PlanningData) => {
    if (DEMO_MODE) {
      return Promise.resolve(createDemoResponse({ success: true }));
    }
    return api.put(`/plannings/${id}`, data);
  },
  deletePlanning: (id: string) => {
    if (DEMO_MODE) {
      return Promise.resolve(createDemoResponse({ success: true }));
    }
    return api.delete(`/plannings/${id}`);
  },
};

// AI API with demo mode
export const aiApi = {
  chat: (message: string, context?: AIContext) => {
    if (DEMO_MODE) {
      return Promise.resolve(
        createDemoResponse({
          response:
            "¡Hola! Soy tu asistente de planeación NEM. ¿En qué puedo ayudarte?",
        })
      );
    }
    return api.post("/ai/chat", { message, context });
  },

  generateContent: (type: string, context?: AIContext) => {
    if (DEMO_MODE) {
      return Promise.resolve(
        createDemoResponse({
          content: "Contenido de ejemplo generado para: " + type,
        })
      );
    }
    return api.post("/ai/generate", { type, context });
  },

  validatePlanning: (planning: PlanningData) => {
    if (DEMO_MODE) {
      return Promise.resolve(
        createDemoResponse({
          valid: true,
          suggestions: ["Considera agregar más actividades prácticas"],
        })
      );
    }
    return api.post("/ai/validate", { planning });
  },
};

// Export the main api instance as both default and named export
export { api };
export default api;
