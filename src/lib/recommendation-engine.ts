import { SEPIntegration, SEPResource } from './sep-integration';

interface RecommendationFilters {
  grade?: string;
  subject?: string;
  type?: string;
}

interface RecommendationScore {
  resource: SEPResource;
  score: number;
}

export class RecommendationEngine {
  private static instance: RecommendationEngine;
  private sepIntegration: SEPIntegration;

  private constructor() {
    this.sepIntegration = new SEPIntegration();
  }

  public static getInstance(): RecommendationEngine {
    if (!RecommendationEngine.instance) {
      RecommendationEngine.instance = new RecommendationEngine();
    }
    return RecommendationEngine.instance;
  }

  async getPersonalizedRecommendations(filters: RecommendationFilters = {}): Promise<RecommendationScore[]> {
    try {
      // Get base resources
      const baseResources = await this.sepIntegration.getResources({
        grade: filters.grade,
        subject: filters.subject,
        type: filters.type,
        limit: 20
      });

      // Calculate scores based on multiple factors
      const scoredRecommendations = baseResources.resources.map((resource: SEPResource) => ({
        resource,
        score: this.calculateScore(resource, filters)
      }));

      // Sort by score and return top recommendations
      return scoredRecommendations
        .sort((a: RecommendationScore, b: RecommendationScore) => b.score - a.score)
        .slice(0, 10);
    } catch (error) {
      console.error('Error generating recommendations:', error);
      return [];
    }
  }

  private calculateScore(resource: SEPResource, filters: RecommendationFilters): number {
    let score = 0;

    // Base score from rating and downloads
    score += resource.rating * 10;
    score += Math.log(resource.downloads + 1) * 5;

    // Bonus for official resources
    if (resource.isOfficial) {
      score += 20;
    }

    // Bonus for matching filters
    if (filters.grade && resource.grade === filters.grade) {
      score += 15;
    }
    if (filters.subject && resource.subject === filters.subject) {
      score += 15;
    }
    if (filters.type && resource.type === filters.type) {
      score += 10;
    }

    // Recency bonus
    const daysSinceCreation = (Date.now() - new Date(resource.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 10 - daysSinceCreation / 30);

    return score;
  }

  async getTrendingResources(limit: number = 10): Promise<SEPResource[]> {
    try {
      const resources = await this.sepIntegration.getResources({ limit: 50 });
      
      return resources.resources
        .sort((a: SEPResource, b: SEPResource) => b.downloads - a.downloads)
        .slice(0, limit);
    } catch (error) {
      console.error('Error fetching trending resources:', error);
      return [];
    }
  }

  async getNewResources(limit: number = 10): Promise<SEPResource[]> {
    try {
      const resources = await this.sepIntegration.getResources({ limit: 50 });
      
      return resources.resources
        .sort((a: SEPResource, b: SEPResource) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Error fetching new resources:', error);
      return [];
    }
  }

  async getResourcesByGrade(grade: string): Promise<SEPResource[]> {
    try {
      const resources = await this.sepIntegration.getResources({ grade, limit: 20 });
      return resources.resources;
    } catch (error) {
      console.error('Error fetching resources by grade:', error);
      return [];
    }
  }

  async getResourcesBySubject(subject: string): Promise<SEPResource[]> {
    try {
      const resources = await this.sepIntegration.getResources({ subject, limit: 20 });
      return resources.resources;
    } catch (error) {
      console.error('Error fetching resources by subject:', error);
      return [];
    }
  }
}

export const recommendationEngine = RecommendationEngine.getInstance();