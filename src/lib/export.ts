import jsPDF from 'jspdf';

// Define PlanningData interface locally since it's not exported from api.ts
export interface PlanningData {
  id?: string;
  title?: string;
  description?: string;
  grade?: string;
  subject?: string;
  duration?: string;
  objectives?: string[];
  content?: Record<string, unknown>;
  [key: string]: unknown;
}

// Types for export functionality
export interface ExportOptions {
  format: 'pdf' | 'word' | 'json' | 'html' | 'text';
  includeSections: string[];
  template: 'planeacion-nem' | 'actividad-socioconstructivista' | 'proyecto-comunitario';
  language: 'es' | 'en';
  includeMetadata: boolean;
}

interface PDFStyle {
  fontSize: number;
  lineHeight: number;
  margin: number;
  headerColor: [number, number, number];
  textColor: [number, number, number];
  backgroundColor: [number, number, number];
}

interface PDFTemplate {
  name: string;
  sections: string[];
  styles: PDFStyle;
  header: string;
  footer: string;
}

// Official SEP templates
export const OFFICIAL_TEMPLATES: Record<string, PDFTemplate> = {
  'planeacion-nem': {
    name: 'Planeación NEM Oficial',
    sections: [
      'datos-generales',
      'campo-formativo',
      'pda',
      'actividades',
      'evaluacion',
      'recursos',
      'reflexion'
    ],
    styles: {
      fontSize: 12,
      lineHeight: 1.5,
      margin: 20,
      headerColor: [0, 51, 102],
      textColor: [0, 0, 0],
      backgroundColor: [255, 255, 255]
    },
    header: 'PLANEACIÓN DIDÁCTICA - NUEVA ESCUELA MEXICANA',
    footer: 'Generado por Planeación NEM - © 2024'
  },
  'actividad-socioconstructivista': {
    name: 'Actividad Socioconstructivista',
    sections: [
      'titulo',
      'objetivos',
      'materiales',
      'desarrollo',
      'evaluacion',
      'reflexion'
    ],
    styles: {
      fontSize: 11,
      lineHeight: 1.4,
      margin: 15,
      headerColor: [0, 128, 0],
      textColor: [0, 0, 0],
      backgroundColor: [255, 255, 255]
    },
    header: 'ACTIVIDAD SOCIOCONSTRUCTIVISTA - NEM',
    footer: 'Actividad alineada con la Nueva Escuela Mexicana'
  },
  'proyecto-comunitario': {
    name: 'Proyecto Comunitario',
    sections: [
      'titulo',
      'diagnostico',
      'objetivos',
      'planificacion',
      'implementacion',
      'evaluacion',
      'impacto'
    ],
    styles: {
      fontSize: 11,
      lineHeight: 1.4,
      margin: 15,
      headerColor: [128, 0, 128],
      textColor: [0, 0, 0],
      backgroundColor: [255, 255, 255]
    },
    header: 'PROYECTO COMUNITARIO - NUEVA ESCUELA MEXICANA',
    footer: 'Proyecto orientado a la transformación comunitaria'
  }
};

// Export class for handling different formats
export class DocumentExporter {
  private doc: jsPDF | null = null;

  constructor() {
    this.doc = new jsPDF();
  }

  // Generate PDF with official SEP format
  async generatePDF(planning: PlanningData, template: string = 'planeacion-nem'): Promise<Blob> {
    const selectedTemplate = OFFICIAL_TEMPLATES[template] || OFFICIAL_TEMPLATES['planeacion-nem'];
    
    if (!this.doc) {
      throw new Error('PDF generator not initialized');
    }

    const doc = new jsPDF();
    
    // Add header
    doc.setFontSize(16);
    doc.setTextColor(
      selectedTemplate.styles.headerColor[0],
      selectedTemplate.styles.headerColor[1],
      selectedTemplate.styles.headerColor[2]
    );
    doc.text(selectedTemplate.header, 20, 20);
    
    // Add metadata
    doc.setFontSize(12);
    doc.setTextColor(
      selectedTemplate.styles.textColor[0],
      selectedTemplate.styles.textColor[1],
      selectedTemplate.styles.textColor[2]
    );
    
    let yPosition = 40;
    
    // Datos Generales
    if (planning.title) {
      doc.setFontSize(14);
      doc.text('Título:', 20, yPosition);
      doc.setFontSize(12);
      doc.text(planning.title, 40, yPosition);
      yPosition += 10;
    }
    
    if (planning.grade) {
      doc.text(`Grado: ${planning.grade}`, 20, yPosition);
      yPosition += 10;
    }
    
    if (planning.subject) {
      doc.text(`Materia: ${planning.subject}`, 20, yPosition);
      yPosition += 10;
    }
    
    if (planning.duration) {
      doc.text(`Duración: ${planning.duration}`, 20, yPosition);
      yPosition += 15;
    }
    
    // Objectivos
    if (planning.objectives && planning.objectives.length > 0) {
      doc.setFontSize(14);
      doc.text('Objetivos de Aprendizaje:', 20, yPosition);
      yPosition += 10;
      
      doc.setFontSize(12);
      planning.objectives.forEach((obj: string, index: number) => {
        doc.text(`${index + 1}. ${obj}`, 25, yPosition);
        yPosition += 8;
      });
      yPosition += 10;
    }
    
    // Contenido
    if (planning.content) {
      doc.setFontSize(14);
      doc.text('Contenido y Actividades:', 20, yPosition);
      yPosition += 10;
      
      doc.setFontSize(12);
      const contentText = JSON.stringify(planning.content, null, 2);
      const splitText = doc.splitTextToSize(contentText, 170);
      doc.text(splitText, 20, yPosition);
      yPosition += splitText.length * 5 + 10;
    }
    
    // Footer
    doc.setFontSize(10);
    doc.setTextColor(128, 128, 128);
    doc.text(selectedTemplate.footer, 20, doc.internal.pageSize.height - 20);
    
    return doc.output('blob');
  }

  // Generate Word document (simplified HTML)
  generateWord(planning: PlanningData): string {
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${planning.title || 'Planeación NEM'}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          .header { background-color: #003366; color: white; padding: 20px; text-align: center; }
          .section { margin: 20px 0; }
          .section-title { color: #003366; font-size: 18px; font-weight: bold; border-bottom: 2px solid #003366; padding-bottom: 5px; }
          .content { margin: 10px 0; }
          .footer { margin-top: 40px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>PLANEACIÓN DIDÁCTICA - NUEVA ESCUELA MEXICANA</h1>
          <h2>${planning.title || 'Sin título'}</h2>
        </div>
        
        <div class="section">
          <div class="section-title">Datos Generales</div>
          <div class="content">
            <p><strong>Grado:</strong> ${planning.grade || 'No especificado'}</p>
            <p><strong>Materia:</strong> ${planning.subject || 'No especificada'}</p>
            <p><strong>Duración:</strong> ${planning.duration || 'No especificada'}</p>
          </div>
        </div>
        
        <div class="section">
          <div class="section-title">Objetivos de Aprendizaje</div>
          <div class="content">
            ${planning.objectives?.map((obj: string) => `<p>• ${obj}</p>`).join('') || '<p>No especificados</p>'}
          </div>
        </div>
        
        <div class="section">
          <div class="section-title">Contenido y Actividades</div>
          <div class="content">
            <pre>${JSON.stringify(planning.content, null, 2)}</pre>
          </div>
        </div>
        
        <div class="footer">
          <p>Generado por Planeación NEM - Nueva Escuela Mexicana</p>
          <p>Fecha: ${new Date().toLocaleDateString('es-MX')}</p>
        </div>
      </body>
      </html>
    `;
    
    return html;
  }

  // Generate JSON export
  generateJSON(planning: PlanningData): string {
    return JSON.stringify({
      metadata: {
        version: '1.0',
        generatedAt: new Date().toISOString(),
        format: 'planeacion-nem'
      },
      planning: planning
    }, null, 2);
  }

  // Generate simple text format
  generateText(planning: PlanningData): string {
    let text = `PLANEACIÓN DIDÁCTICA - NUEVA ESCUELA MEXICANA\n`;
    text += `=============================================\n\n`;
    
    if (planning.title) {
      text += `Título: ${planning.title}\n`;
    }
    
    if (planning.grade) {
      text += `Grado: ${planning.grade}\n`;
    }
    
    if (planning.subject) {
      text += `Materia: ${planning.subject}\n`;
    }
    
    if (planning.duration) {
      text += `Duración: ${planning.duration}\n`;
    }
    
    text += `\nOBJETIVOS DE APRENDIZAJE\n`;
    text += `------------------------\n`;
    if (planning.objectives) {
      planning.objectives.forEach((obj: string, index: number) => {
        text += `${index + 1}. ${obj}\n`;
      });
    }
    
    text += `\nCONTENIDO Y ACTIVIDADES\n`;
    text += `-----------------------\n`;
    if (planning.content) {
      text += JSON.stringify(planning.content, null, 2);
    }
    
    text += `\n\nGenerado por Planeación NEM - ${new Date().toLocaleDateString('es-MX')}`;
    
    return text;
  }

  // Main export function
  async exportDocument(
    planning: PlanningData,
    options: ExportOptions
  ): Promise<Blob | string> {
    switch (options.format) {
      case 'pdf':
        return await this.generatePDF(planning, options.template);
      case 'word':
        return this.generateWord(planning);
      case 'json':
        return this.generateJSON(planning);
      case 'html':
        return this.generateWord(planning);
      case 'text':
        return this.generateText(planning);
      default:
        throw new Error(`Formato no soportado: ${options.format}`);
    }
  }

  // Download helper
  downloadFile(content: Blob | string, filename: string, format: string) {
    let blob: Blob;
    let mimeType: string;
    
    if (typeof content === 'string') {
      switch (format) {
        case 'word':
        case 'html':
          mimeType = 'text/html';
          filename += '.html';
          break;
        case 'json':
          mimeType = 'application/json';
          filename += '.json';
          break;
        case 'text':
          mimeType = 'text/plain';
          filename += '.txt';
          break;
        default:
          mimeType = 'text/plain';
          filename += '.txt';
      }
      blob = new Blob([content], { type: mimeType });
    } else {
      blob = content;
      if (format === 'pdf') {
        filename += '.pdf';
      }
    }
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

// Export utilities
export const exportUtils = {
  DocumentExporter,
  OFFICIAL_TEMPLATES,
  
  // Quick export functions
  async exportToPDF(planning: PlanningData, filename: string = 'planeacion-nem') {
    const exporter = new DocumentExporter();
    const blob = await exporter.exportDocument(planning, {
      format: 'pdf',
      template: 'planeacion-nem',
      includeSections: ['all'],
      language: 'es',
      includeMetadata: true
    });
    
    if (blob instanceof Blob) {
      exporter.downloadFile(blob, filename, 'pdf');
    }
  },
  
  exportToWord(planning: PlanningData, filename: string = 'planeacion-nem') {
    const exporter = new DocumentExporter();
    const content = exporter.exportDocument(planning, {
      format: 'word',
      template: 'planeacion-nem',
      includeSections: ['all'],
      language: 'es',
      includeMetadata: true
    });
    
    if (typeof content === 'string') {
      exporter.downloadFile(content, filename, 'word');
    }
  },
  
  exportToJSON(planning: PlanningData, filename: string = 'planeacion-nem') {
    const exporter = new DocumentExporter();
    const content = exporter.exportDocument(planning, {
      format: 'json',
      template: 'planeacion-nem',
      includeSections: ['all'],
      language: 'es',
      includeMetadata: true
    });
    
    if (typeof content === 'string') {
      exporter.downloadFile(content, filename, 'json');
    }
  }
};