import { WizardData } from '../stores/planningWizardStore';

export interface ValidationResult {
  score: number;
  maxScore: number;
  percentage: number;
  level: 'excellent' | 'good' | 'fair' | 'needs-improvement';
  categories: ValidationCategory[];
  recommendations: string[];
  warnings: string[];
  errors: string[];
}

export interface ValidationCategory {
  name: string;
  score: number;
  maxScore: number;
  percentage: number;
  details: string[];
}

export interface LearningObjective {
  id: string;
  description: string;
  grade: string;
  subject: string;
}

export class ValidationEngine {
  constructor() {}

  async validatePlanning(planning: WizardData): Promise<ValidationResult> {
    const categories = await Promise.all([
      this.validateCurricularAlignment(planning),
      this.validateMethodology(planning),
      this.validateEvaluation(planning),
      this.validateInclusion(planning),
      this.validateCompleteness(planning)
    ]);

    const totalScore = categories.reduce((sum, cat) => sum + cat.score, 0);
    const totalMaxScore = categories.reduce((sum, cat) => sum + cat.maxScore, 0);
    const percentage = Math.round((totalScore / totalMaxScore) * 100);

    const level = this.getValidationLevel(percentage);
    const recommendations = this.generateRecommendations(categories);
    const warnings = this.generateWarnings(categories);
    const errors = this.generateErrors(categories);

    return {
      score: totalScore,
      maxScore: totalMaxScore,
      percentage,
      level,
      categories,
      recommendations,
      warnings,
      errors
    };
  }

  private async validateCurricularAlignment(planning: WizardData): Promise<ValidationCategory> {
    let score = 0;
    const maxScore = 25;
    const details: string[] = [];

    // Check if learning objectives align with SEP standards
    if (planning.step3?.objectives && planning.step3.objectives.length > 0) {
      // Mock learning objectives for now - in real implementation, fetch from SEP
      const sepObjectives: LearningObjective[] = [
        { id: '1', description: 'Desarrollar competencias comunicativas', grade: planning.step1?.grade || '', subject: planning.step1?.subject || '' },
        { id: '2', description: 'Resolver problemas matemáticos', grade: planning.step1?.grade || '', subject: planning.step1?.subject || '' }
      ];
      
      const alignedObjectives = planning.step3.objectives.filter(obj => 
        sepObjectives.some(sepObj => 
          sepObj.description.toLowerCase().includes(obj.toLowerCase()) || 
          obj.toLowerCase().includes(sepObj.description.toLowerCase())
        )
      );
      
      if (alignedObjectives.length > 0) {
        score += 15;
        details.push(`✓ ${alignedObjectives.length} objetivos alineados con SEP`);
      } else {
        details.push('⚠ Los objetivos de aprendizaje no parecen alinearse con los estándares SEP');
      }
    }

    // Check field of study alignment
    if (planning.step2?.formativeField && planning.step2.articulatingAxes && planning.step2.articulatingAxes.length > 0) {
      score += 10;
      details.push('✓ Campo formativo y ejes curriculares definidos');
    } else {
      details.push('⚠ Falta definir campo formativo o ejes curriculares');
    }

    return {
      name: 'Alineación Curricular',
      score,
      maxScore,
      percentage: Math.round((score / maxScore) * 100),
      details
    };
  }

  private validateMethodology(planning: WizardData): ValidationCategory {
    let score = 0;
    const maxScore = 20;
    const details: string[] = [];

    // Check for socioconstructivist methodology
    if (planning.step4?.activities && planning.step4.activities.length > 0) {
      const socioconstructivistKeywords = [
        'colaborativo', 'cooperativo', 'diálogo', 'investigación', 'proyecto',
        'resolución de problemas', 'aprendizaje significativo', 'social', 'comunitario'
      ];

      const hasSocioconstructivistElements = planning.step4.activities.some(activity =>
        socioconstructivistKeywords.some(keyword => 
          activity.description?.toLowerCase().includes(keyword) ||
          activity.name?.toLowerCase().includes(keyword)
        )
      );

      if (hasSocioconstructivistElements) {
        score += 15;
        details.push('✓ Metodología socioconstructivista identificada');
      } else {
        details.push('⚠ Las actividades podrían beneficiarse de enfoques socioconstructivistas');
      }

      // Check for student-centered activities
      const studentCenteredKeywords = ['alumno', 'estudiante', 'aprendiz', 'descubrimiento', 'exploración'];
      const hasStudentCentered = planning.step4.activities.some(activity =>
        studentCenteredKeywords.some(keyword => 
          activity.description?.toLowerCase().includes(keyword)
        )
      );

      if (hasStudentCentered) {
        score += 5;
        details.push('✓ Enfoque centrado en el estudiante');
      }
    }

    return {
      name: 'Metodología Socioconstructivista',
      score,
      maxScore,
      percentage: Math.round((score / maxScore) * 100),
      details
    };
  }

  private validateEvaluation(planning: WizardData): ValidationCategory {
    let score = 0;
    const maxScore = 20;
    const details: string[] = [];

    // Check for formative evaluation
    if (planning.step5?.evaluation?.instruments && planning.step5.evaluation.instruments.length > 0) {
      const formativeKeywords = ['formativa', 'diagnóstica', 'continua', 'proceso'];
      const hasFormative = planning.step5.evaluation.instruments.some(instrument =>
        formativeKeywords.some(keyword => instrument.toLowerCase().includes(keyword))
      );

      if (hasFormative) {
        score += 15;
        details.push(`✓ Instrumentos de evaluación formativa presentes`);
      } else {
        score += 10;
        details.push('⚠ Considerar instrumentos de evaluación formativa');
      }

      // Check for rubrics or criteria
      if (planning.step5.evaluation.criteria && planning.step5.evaluation.criteria.length > 0) {
        score += 5;
        details.push('✓ Criterios de evaluación definidos');
      } else {
        details.push('⚠ Considerar agregar rúbricas o criterios claros de evaluación');
      }
    } else {
      details.push('⚠ Falta definir instrumentos de evaluación');
    }

    return {
      name: 'Evaluación Formativa',
      score,
      maxScore,
      percentage: Math.round((score / maxScore) * 100),
      details
    };
  }

  private validateInclusion(planning: WizardData): ValidationCategory {
    let score = 0;
    const maxScore = 15;
    const details: string[] = [];

    // Check for inclusive language and considerations
    const inclusiveKeywords = [
      'inclusión', 'diversidad', 'equidad', 'diferentes ritmos', 'necesidades especiales',
      'contexto cultural', 'lengua materna', 'género', 'accesibilidad'
    ];

    const planningText = JSON.stringify(planning).toLowerCase();
    const hasInclusiveElements = inclusiveKeywords.some(keyword => planningText.includes(keyword));

    if (hasInclusiveElements) {
      score += 10;
      details.push('✓ Consideraciones de inclusión presentes');
    } else {
      details.push('⚠ Podría fortalecerse con consideraciones de inclusión educativa');
    }

    // Check for differentiated activities
    const hasDifferentiation = planning.step4?.activities?.some(activity => 
      activity.description?.toLowerCase().includes('diferenciad') || 
      activity.description?.toLowerCase().includes('adaptad')
    );

    if (hasDifferentiation) {
      score += 5;
      details.push('✓ Actividades diferenciadas identificadas');
    }

    return {
      name: 'Inclusión Educativa',
      score,
      maxScore,
      percentage: Math.round((score / maxScore) * 100),
      details
    };
  }

  private validateCompleteness(planning: WizardData): ValidationCategory {
    let score = 0;
    const maxScore = 20;
    const details: string[] = [];

    // Check all required fields
    const requiredFields = [
      planning.step1?.title,
      planning.step1?.subject,
      planning.step1?.grade,
      planning.step2?.formativeField,
      planning.step2?.articulatingAxes,
      planning.step3?.objectives,
      planning.step4?.activities,
      planning.step5?.evaluation
    ];

    const completedFields = requiredFields.filter(field => {
      if (Array.isArray(field)) return field.length > 0;
      return field !== undefined && field !== null && field !== '';
    });

    score = Math.round((completedFields.length / requiredFields.length) * maxScore);

    if (completedFields.length === requiredFields.length) {
      details.push('✓ Todos los campos requeridos completos');
    } else {
      const missingCount = requiredFields.length - completedFields.length;
      details.push(`⚠ ${missingCount} campos requeridos faltantes`);
    }

    return {
      name: 'Completitud de la Planeación',
      score,
      maxScore,
      percentage: Math.round((score / maxScore) * 100),
      details
    };
  }

  private getValidationLevel(percentage: number): 'excellent' | 'good' | 'fair' | 'needs-improvement' {
    if (percentage >= 90) return 'excellent';
    if (percentage >= 75) return 'good';
    if (percentage >= 60) return 'fair';
    return 'needs-improvement';
  }

  private generateRecommendations(categories: ValidationCategory[]): string[] {
    const recommendations: string[] = [];

    categories.forEach(category => {
      if (category.percentage < 75) {
        switch (category.name) {
          case 'Alineación Curricular':
            recommendations.push('Revise la alineación de sus objetivos con los estándares SEP oficiales');
            break;
          case 'Metodología Socioconstructivista':
            recommendations.push('Incorpore más actividades colaborativas y centradas en el estudiante');
            break;
          case 'Evaluación Formativa':
            recommendations.push('Agregue instrumentos de evaluación formativa como rúbricas o autoevaluaciones');
            break;
          case 'Inclusión Educativa':
            recommendations.push('Considere estrategias para atender la diversidad del aula');
            break;
          case 'Completitud de la Planeación':
            recommendations.push('Complete todos los campos requeridos para una planeación integral');
            break;
        }
      }
    });

    return recommendations;
  }

  private generateWarnings(categories: ValidationCategory[]): string[] {
    const warnings: string[] = [];

    categories.forEach(category => {
      if (category.percentage < 60) {
        warnings.push(`${category.name}: ${category.percentage}% - Necesita atención inmediata`);
      }
    });

    return warnings;
  }

  private generateErrors(categories: ValidationCategory[]): string[] {
    const errors: string[] = [];

    // Critical errors that would prevent implementation
    const completeness = categories.find(c => c.name === 'Completitud de la Planeación');
    if (completeness && completeness.percentage < 50) {
      errors.push('La planeación está incompleta y no puede ser implementada');
    }

    return errors;
  }
}

export const validationEngine = new ValidationEngine();