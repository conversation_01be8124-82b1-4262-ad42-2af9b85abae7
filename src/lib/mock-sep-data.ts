import { SEPResource } from './sep-integration';

export const mockSEPResources: SEPResource[] = [
  {
    id: '1',
    title: 'Planeación de Lenguaje y Comunicación - Primer Grado',
    type: 'planeacion',
    category: 'Lenguaje y Comunicación',
    grade: '1°',
    subject: 'Lenguaje y Comunicación',
    content: {
      objetivo: 'Desarrollar habilidades de lectura y escritura',
      actividades: [
        'Lectura de cuentos',
        'Escritura de oraciones simples',
        'Juegos de palabras'
      ],
      duracion: '4 semanas',
      recursos: ['Libros de cuentos', 'Lápices', 'Papel']
    },
    tags: ['lectura', 'escritura', 'primer grado', 'lenguaje'],
    isOfficial: true,
    downloads: 125,
    rating: 4.8,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    title: 'Actividad de Matemáticas - Segundo Grado',
    type: 'actividad',
    category: 'Pensamiento Matemático',
    grade: '2°',
    subject: 'Matemáticas',
    content: {
      objetivo: 'Comprender la suma y resta con llevadas',
      actividades: [
        'Resolución de problemas',
        'Uso de material concreto',
        'Juegos matemáticos'
      ],
      duracion: '2 semanas',
      recursos: ['Fichas', 'Abaco', 'Problemas impresos']
    },
    tags: ['suma', 'resta', 'segundo grado', 'matemáticas'],
    isOfficial: true,
    downloads: 89,
    rating: 4.6,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    title: 'Proyecto Comunitario - Medio Ambiente',
    type: 'proyecto',
    category: 'Exploración y Comprensión del Mundo Natural y Social',
    grade: '3°',
    subject: 'Ciencias Naturales',
    content: {
      objetivo: 'Cuidar el medio ambiente en la comunidad',
      actividades: [
        'Recolección de basura',
        'Plantación de árboles',
        'Campaña de concientización'
      ],
      duracion: '3 semanas',
      recursos: ['Guantes', 'Bolsas', 'Plantas']
    },
    tags: ['medio ambiente', 'comunidad', 'proyecto', 'ciencias'],
    isOfficial: true,
    downloads: 67,
    rating: 4.7,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '4',
    title: 'Rúbrica de Evaluación - Expresión Artística',
    type: 'rubrica',
    category: 'Expresión y Apreciación Artísticas',
    grade: '1°-3°',
    subject: 'Artes',
    content: {
      criterios: [
        'Participación activa',
        'Creatividad',
        'Uso de materiales',
        'Trabajo en equipo'
      ],
      niveles: ['Excelente', 'Bueno', 'Suficiente', 'Necesita mejorar'],
      descripcion: 'Evaluación del desempeño en actividades artísticas'
    },
    tags: ['evaluación', 'artes', 'rúbrica', 'primaria'],
    isOfficial: true,
    downloads: 156,
    rating: 4.9,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '5',
    title: 'Planeación de Educación Cívica - Tercer Grado',
    type: 'planeacion',
    category: 'Educación Cívica y Ética',
    grade: '3°',
    subject: 'Formación Cívica y Ética',
    content: {
      objetivo: 'Desarrollar valores cívicos y responsabilidad social',
      actividades: [
        'Dramatizaciones',
        'Debates',
        'Visitas comunitarias'
      ],
      duracion: '5 semanas',
      recursos: ['Material audiovisual', 'Visitas guiadas']
    },
    tags: ['cívica', 'ética', 'valores', 'tercer grado'],
    isOfficial: true,
    downloads: 78,
    rating: 4.5,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

interface ResourceFilters {
  grade?: string;
  subject?: string;
  type?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

interface CreateResourceData {
  title: string;
  type: string;
  category: string;
  grade?: string;
  subject?: string;
  content: Record<string, unknown>;
  tags?: string[];
  isOfficial: boolean;
}

export const mockSEPIntegration = {
  async getResources(filters: ResourceFilters = {}) {
    let filtered = [...mockSEPResources];
    
    if (filters.type && filters.type !== 'all') {
      filtered = filtered.filter(r => r.type === filters.type);
    }
    
    if (filters.grade && filters.grade !== 'all') {
      filtered = filtered.filter(r => r.grade === filters.grade);
    }
    
    if (filters.subject && filters.subject !== 'all') {
      filtered = filtered.filter(r => r.subject === filters.subject);
    }
    
    if (filters.search) {
      filtered = filtered.filter(r => 
        r.title.toLowerCase().includes(filters.search?.toLowerCase() || '') ||
        r.category.toLowerCase().includes(filters.search?.toLowerCase() || '')
      );
    }
    
    return {
      resources: filtered,
      total: filtered.length,
      limit: filters.limit || 50,
      offset: filters.offset || 0
    };
  },

  async getResourceById(id: string) {
    return mockSEPResources.find(r => r.id === id) || mockSEPResources[0];
  },

  async getSubjectsByGrade(grade: string) {
    const subjects = mockSEPResources
      .filter(r => r.grade === grade)
      .map(r => r.subject)
      .filter((subject): subject is string => subject !== undefined);
    return subjects;
  },

  async getResourceTypes() {
    return ['planeacion', 'actividad', 'proyecto', 'rubrica', 'evaluacion'];
  },

  async getRecommendations() {
    return mockSEPResources.slice(0, 5);
  },

  async createResource(resource: CreateResourceData) {
    const newResource = {
      ...resource,
      id: String(mockSEPResources.length + 1),
      downloads: 0,
      rating: 5,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockSEPResources.push(newResource);
    return newResource;
  },

  async incrementDownload(id: string) {
    const resource = mockSEPResources.find(r => r.id === id);
    if (resource) {
      resource.downloads += 1;
    }
  }
};