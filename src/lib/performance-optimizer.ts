// Performance optimization utilities

// Cache management
export interface CachedData<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

export const cacheManager = {
  // Cache API responses
  cacheApiResponse: <T>(key: string, data: T, ttl = 3600000): void => {
    const item: CachedData<T> = {
      data,
      timestamp: Date.now(),
      ttl,
    };
    localStorage.setItem(`cache_${key}`, JSON.stringify(item));
  },

  // Get cached response
  getCachedResponse: <T>(key: string): T | null => {
    const cached = localStorage.getItem(`cache_${key}`);
    if (!cached) return null;

    try {
      const { data, timestamp, ttl } = JSON.parse(cached) as CachedData<T>;
      if (Date.now() - timestamp > ttl) {
        localStorage.removeItem(`cache_${key}`);
        return null;
      }
      return data;
    } catch {
      return null;
    }
  },

  // Clear expired cache
  clearExpiredCache: (): void => {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('cache_')) {
        const cached = localStorage.getItem(key);
        if (cached) {
          try {
            const { timestamp, ttl } = JSON.parse(cached) as CachedData<unknown>;
            if (Date.now() - timestamp > ttl) {
              localStorage.removeItem(key);
            }
          } catch {
            localStorage.removeItem(key);
          }
        }
      }
    });
  },
};

// Performance monitoring
declare global {
  interface Window {
    gtag?: (...args: unknown[]) => void;
  }
}

interface PerformanceEntryWithProcessingStart extends PerformanceEntry {
  processingStart?: number;
}

interface PerformanceEntryWithHadRecentInput extends PerformanceEntry {
  hadRecentInput?: boolean;
}

interface PerformanceEntryWithValue extends PerformanceEntry {
  value?: number;
}

export const performanceMonitor = {
  // Measure page load time
  measurePageLoad: (): void => {
    if ('performance' in window) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
          console.log(`Page load time: ${loadTime}ms`);
          
          // Send to analytics
          if (window.gtag) {
            window.gtag('event', 'page_load_time', {
              value: Math.round(loadTime),
            });
          }
        }, 0);
      });
    }
  },

  // Monitor Core Web Vitals
  monitorWebVitals: (): void => {
    if ('PerformanceObserver' in window) {
      // LCP (Largest Contentful Paint)
      try {
        new PerformanceObserver((entryList) => {
          for (const entry of entryList.getEntries()) {
            console.log('LCP:', entry.startTime);
          }
        }).observe({ entryTypes: ['largest-contentful-paint'] });
      } catch {
        console.warn('LCP monitoring not supported');
      }

      // FID (First Input Delay)
      try {
        new PerformanceObserver((entryList) => {
          for (const entry of entryList.getEntries()) {
            const processingEntry = entry as PerformanceEntryWithProcessingStart;
            if (processingEntry.processingStart) {
              console.log('FID:', processingEntry.processingStart - entry.startTime);
            }
          }
        }).observe({ entryTypes: ['first-input'] });
      } catch {
        console.warn('FID monitoring not supported');
      }

      // CLS (Cumulative Layout Shift)
      try {
        new PerformanceObserver((entryList) => {
          for (const entry of entryList.getEntries()) {
            const clsEntry = entry as PerformanceEntryWithHadRecentInput & PerformanceEntryWithValue;
            if (!clsEntry.hadRecentInput && clsEntry.value) {
              console.log('CLS:', clsEntry.value);
            }
          }
        }).observe({ entryTypes: ['layout-shift'] });
      } catch {
        console.warn('CLS monitoring not supported');
      }
    }
  },
};

// Debounce utility for performance
export const debounce = <T extends (...args: unknown[]) => void>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Throttle utility for performance
export const throttle = <T extends (...args: unknown[]) => void>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// Service Worker registration for offline support
export const registerServiceWorker = async (): Promise<void> => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('SW registered: ', registration);
    } catch (error) {
      console.log('SW registration failed: ', error);
    }
  }
};

// Preload critical resources
export const preloadCriticalResources = (): void => {
  const criticalResources: string[] = [
    // Add actual critical API endpoints here when needed
    // '/api/auth/me',
    // '/api/resources/featured',
  ];

  criticalResources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'fetch';
    link.href = resource;
    document.head.appendChild(link);
  });
};

// Image optimization
export const imageOptimizer = {
  // Preload critical images
  preloadImages: (urls: string[]): void => {
    urls.forEach(url => {
      const img = new Image();
      img.src = url;
    });
  },

  // Generate responsive image URLs
  getResponsiveImage: (src: string, width: number): string => {
    // In a real app, this would integrate with a CDN
    return `${src}?w=${width}`;
  },
};

// Initialize performance optimizations
export const initializePerformance = (): void => {
  performanceMonitor.measurePageLoad();
  performanceMonitor.monitorWebVitals();
  preloadCriticalResources();
  cacheManager.clearExpiredCache();
};