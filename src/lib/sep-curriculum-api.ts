import { api } from './api';

// Types for SEP Curriculum
export interface SEPFramework {
  id: string;
  name: string;
  version: string;
  phase: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SEPFormativeField {
  id: string;
  frameworkId: string;
  name: string;
  code: string;
  description: string;
  gradeRange: string;
  order: number;
  isActive: boolean;
}

export interface SEPArticulatingAxis {
  id: string;
  fieldId: string;
  name: string;
  code: string;
  description: string;
  grade: string;
  order: number;
  isActive: boolean;
}

export interface SEPLearningPurpose {
  id: string;
  axisId: string;
  fieldId: string;
  grade: string;
  subject: string;
  purpose: string;
  code: string;
  description: string;
  expectedLearning: string[];
  indicators: string[];
  field?: {
    id: string;
    name: string;
    code: string;
  };
  axis?: {
    id: string;
    name: string;
    code: string;
  };
}

export interface SEPProject {
  id: string;
  name: string;
  grade: string;
  subject: string;
  type: string;
  duration: string;
  description: string;
  objectives: string[];
  phases: Array<{
    name: string;
    duration: string;
    activities: string[];
  }>;
  resources: string[];
  evaluationCriteria: string[];
  isActive: boolean;
}

export interface SEPNormativeDocument {
  id: string;
  title: string;
  documentType: string;
  number: string;
  date: string;
  description: string;
  content: any;
  url: string;
  category: string;
  isActive: boolean;
}

export interface SEPOfficialTemplate {
  id: string;
  name: string;
  type: string;
  grade?: string;
  subject?: string;
  description: string;
  structure: any;
  example?: any;
  guidelines?: string[];
  isActive: boolean;
}

export interface SEPAssessmentInstrument {
  id: string;
  name: string;
  type: string;
  grade?: string;
  subject?: string;
  description: string;
  structure: any;
  criteria?: any;
  examples?: any;
  isActive: boolean;
}

export interface CurriculumStructure {
  framework: SEPFramework;
  fields: Array<SEPFormativeField & { axes: SEPArticulatingAxis[] }>;
  learningPurposes: SEPLearningPurpose[];
}

// API Service for SEP Curriculum
export class SEPCurriculumService {
  private static instance: SEPCurriculumService;

  public static getInstance(): SEPCurriculumService {
    if (!SEPCurriculumService.instance) {
      SEPCurriculumService.instance = new SEPCurriculumService();
    }
    return SEPCurriculumService.instance;
  }

  // Framework operations
  async getFrameworks(): Promise<SEPFramework[]> {
    const response = await api.get('/sep-curriculum/frameworks');
    return response.data.data;
  }

  async getFormativeFields(frameworkId: string): Promise<SEPFormativeField[]> {
    const response = await api.get(`/sep-curriculum/frameworks/${frameworkId}/fields`);
    return response.data.data;
  }

  // Articulating axes operations
  async getArticulatingAxes(fieldId: string, grade?: string): Promise<SEPArticulatingAxis[]> {
    const params = grade ? { grade } : {};
    const response = await api.get(`/sep-curriculum/fields/${fieldId}/axes`, { params });
    return response.data.data;
  }

  // Learning purposes (PDAs) operations
  async getLearningPurposes(filters?: {
    grade?: string;
    subject?: string;
    field?: string;
    axis?: string;
  }): Promise<SEPLearningPurpose[]> {
    const response = await api.get('/sep-curriculum/learning-purposes', { params: filters });
    return response.data.data;
  }

  // Projects operations
  async getProjects(filters?: {
    grade?: string;
    subject?: string;
    type?: string;
  }): Promise<SEPProject[]> {
    const response = await api.get('/sep-curriculum/projects', { params: filters });
    return response.data.data;
  }

  // Normative documents operations
  async getNormativeDocuments(filters?: {
    category?: string;
    type?: string;
  }): Promise<SEPNormativeDocument[]> {
    const response = await api.get('/sep-curriculum/normative-documents', { params: filters });
    return response.data.data;
  }

  // Official templates operations
  async getOfficialTemplates(filters?: {
    type?: string;
    grade?: string;
    subject?: string;
  }): Promise<SEPOfficialTemplate[]> {
    const response = await api.get('/sep-curriculum/templates', { params: filters });
    return response.data.data;
  }

  // Assessment instruments operations
  async getAssessmentInstruments(filters?: {
    type?: string;
    grade?: string;
    subject?: string;
  }): Promise<SEPAssessmentInstrument[]> {
    const response = await api.get('/sep-curriculum/assessment-instruments', { params: filters });
    return response.data.data;
  }

  // Complete curriculum structure
  async getCurriculumStructure(filters?: {
    grade?: string;
    subject?: string;
  }): Promise<CurriculumStructure> {
    const response = await api.get('/sep-curriculum/curriculum-structure', { params: filters });
    return response.data.data;
  }

  // Utility methods
  async getSubjectsByGrade(grade: string): Promise<string[]> {
    const purposes = await this.getLearningPurposes({ grade });
    return [...new Set(purposes.map(p => p.subject))];
  }

  async getGrades(): Promise<string[]> {
    const purposes = await this.getLearningPurposes();
    return [...new Set(purposes.map(p => p.grade))].sort();
  }

  async getPDAsByGradeAndSubject(grade: string, subject: string): Promise<SEPLearningPurpose[]> {
    return this.getLearningPurposes({ grade, subject });
  }

  async getProjectsByGrade(grade: string): Promise<SEPProject[]> {
    return this.getProjects({ grade });
  }

  async getTemplatesByType(type: string): Promise<SEPOfficialTemplate[]> {
    return this.getOfficialTemplates({ type });
  }
}

// Export singleton instance
export const sepCurriculumService = SEPCurriculumService.getInstance();