import { api } from './api';
import { mockSEPIntegration } from './mock-sep-data';

export interface SEPResource {
  id: string;
  title: string;
  type: string;
  category: string;
  grade?: string;
  subject?: string;
  content: Record<string, unknown>;
  tags?: string[];
  isOfficial: boolean;
  downloads: number;
  rating: number;
  createdAt: string;
  updatedAt: string;
}

export interface SEPFilters {
  grade?: string;
  subject?: string;
  type?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

// Use mock data for development when database is not available
const useMockData = true;

export class SEPIntegration {
  private static instance: SEPIntegration;
  
  public static getInstance(): SEPIntegration {
    if (!SEPIntegration.instance) {
      SEPIntegration.instance = new SEPIntegration();
    }
    return SEPIntegration.instance;
  }

  async getResources(filters: SEPFilters = {}): Promise<{
    resources: SEPResource[];
    total: number;
    limit: number;
    offset: number;
  }> {
    try {
      if (useMockData) {
        return mockSEPIntegration.getResources(filters);
      }
      
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });

      const response = await api.get(`/sep/resources?${params}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching SEP resources:', error);
      return mockSEPIntegration.getResources(filters);
    }
  }

  async getResourceById(id: string): Promise<SEPResource> {
    try {
      if (useMockData) {
        return mockSEPIntegration.getResourceById(id);
      }
      
      const response = await api.get(`/sep/resources/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching SEP resource:', error);
      return mockSEPIntegration.getResourceById(id);
    }
  }

  async getSubjectsByGrade(grade: string): Promise<string[]> {
    try {
      if (useMockData) {
        return mockSEPIntegration.getSubjectsByGrade(grade);
      }
      
      const response = await api.get(`/sep/subjects/${grade}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching subjects:', error);
      return mockSEPIntegration.getSubjectsByGrade(grade);
    }
  }

  async getResourceTypes(): Promise<string[]> {
    try {
      if (useMockData) {
        return mockSEPIntegration.getResourceTypes();
      }
      
      const response = await api.get('/sep/types');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching resource types:', error);
      return mockSEPIntegration.getResourceTypes();
    }
  }

  async getRecommendations(): Promise<SEPResource[]> {
    try {
      if (useMockData) {
        return mockSEPIntegration.getRecommendations();
      }
      
      const response = await api.get('/sep/recommendations');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching recommendations:', error);
      return mockSEPIntegration.getRecommendations();
    }
  }

  async createResource(resource: Omit<SEPResource, 'id' | 'downloads' | 'rating' | 'createdAt' | 'updatedAt'>): Promise<SEPResource> {
    try {
      if (useMockData) {
        return mockSEPIntegration.createResource(resource);
      }
      
      const response = await api.post('/sep/resources', resource);
      return response.data.data;
    } catch (error) {
      console.error('Error creating resource:', error);
      throw new Error('Error al crear recurso');
    }
  }

  async incrementDownload(id: string): Promise<void> {
    try {
      if (useMockData) {
        mockSEPIntegration.incrementDownload(id);
        return;
      }
      
      await api.post(`/sep/resources/${id}/download`);
    } catch (error) {
      console.error('Error incrementing download count:', error);
    }
  }
}

export const sepIntegration = SEPIntegration.getInstance();