// Accessibility utilities for WCAG 2.1 compliance

// Keyboard navigation manager
export class KeyboardNavigation {
  private focusableElements: string[] = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[role="button"]:not([disabled])',
  ];

  private trapFocus(container: HTMLElement, event: KeyboardEvent): void {
    const focusable = container.querySelectorAll(this.focusableElements.join(','));
    const firstElement = focusable[0] as HTMLElement;
    const lastElement = focusable[focusable.length - 1] as HTMLElement;

    if (event.key === 'Tab') {
      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus();
          event.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus();
          event.preventDefault();
        }
      }
    }
  }

  // Enable keyboard navigation for modal dialogs
  enableModalKeyboardNavigation(modalId: string): void {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    modal.addEventListener('keydown', (e) => this.trapFocus(modal, e));
  }

  // Skip to main content link
  createSkipLink(): void {
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Saltar al contenido principal';
    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white p-2 z-50';
    document.body.insertBefore(skipLink, document.body.firstChild);
  }
}

// Screen reader announcements
export class ScreenReaderAnnouncer {
  private announcementElement: HTMLElement;

  constructor() {
    this.announcementElement = document.createElement('div');
    this.announcementElement.setAttribute('aria-live', 'polite');
    this.announcementElement.setAttribute('aria-atomic', 'true');
    this.announcementElement.className = 'sr-only';
    document.body.appendChild(this.announcementElement);
  }

  announce(message: string): void {
    this.announcementElement.textContent = message;
    setTimeout(() => {
      this.announcementElement.textContent = '';
    }, 1000);
  }
}

// Color contrast checker
export class ColorContrastChecker {
  private getLuminance(rgb: number[]): number {
    const [r, g, b] = rgb.map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  private hexToRgb(hex: string): number[] {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [
      parseInt(result[1], 16),
      parseInt(result[2], 16),
      parseInt(result[3], 16)
    ] : [0, 0, 0];
  }

  checkContrast(color1: string, color2: string): { ratio: number; level: string } {
    const rgb1 = this.hexToRgb(color1);
    const rgb2 = this.hexToRgb(color2);
    
    const lum1 = this.getLuminance(rgb1);
    const lum2 = this.getLuminance(rgb2);
    
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    
    const ratio = (brightest + 0.05) / (darkest + 0.05);
    
    let level = 'Fail';
    if (ratio >= 7) level = 'AAA';
    else if (ratio >= 4.5) level = 'AA';
    else if (ratio >= 3) level = 'AA Large';
    
    return { ratio: Math.round(ratio * 100) / 100, level };
  }

  // WCAG compliant color palette
  getCompliantColors(): Record<string, string> {
    return {
      primary: '#2563eb', // Blue-600
      secondary: '#64748b', // Slate-500
      success: '#16a34a', // Green-600
      warning: '#d97706', // Amber-600
      error: '#dc2626', // Red-600
      background: '#ffffff',
      surface: '#f8fafc', // Slate-50
      text: '#1e293b', // Slate-800
      textSecondary: '#475569', // Slate-600
      border: '#e2e8f0', // Slate-200
    };
  }
}

// Focus management
export class FocusManager {
  private previouslyFocusedElement: HTMLElement | null = null;

  saveFocus(): void {
    this.previouslyFocusedElement = document.activeElement as HTMLElement;
  }

  restoreFocus(): void {
    if (this.previouslyFocusedElement) {
      this.previouslyFocusedElement.focus();
      this.previouslyFocusedElement = null;
    }
  }

  setInitialFocus(element: HTMLElement): void {
    element.focus();
  }

  // Create focus trap for modals
  createFocusTrap(container: HTMLElement): () => void {
    const focusableElements = container.querySelectorAll(
      'a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }
}

// ARIA utilities
export class ARIAUtils {
  // Add ARIA labels to interactive elements
  addAriaLabel(element: HTMLElement, label: string): void {
    element.setAttribute('aria-label', label);
  }

  // Add ARIA descriptions
  addAriaDescription(element: HTMLElement, description: string): void {
    element.setAttribute('aria-describedby', description);
  }

  // Mark loading states
  setLoadingState(element: HTMLElement, isLoading: boolean): void {
    if (isLoading) {
      element.setAttribute('aria-busy', 'true');
      element.setAttribute('aria-disabled', 'true');
    } else {
      element.removeAttribute('aria-busy');
      element.removeAttribute('aria-disabled');
    }
  }

  // Announce state changes
  announceStateChange(message: string): void {
    const announcer = new ScreenReaderAnnouncer();
    announcer.announce(message);
  }

  // Create accessible error messages
  createErrorMessage(message: string, elementId: string): HTMLElement {
    const errorDiv = document.createElement('div');
    errorDiv.id = elementId;
    errorDiv.className = 'sr-only';
    errorDiv.setAttribute('role', 'alert');
    errorDiv.setAttribute('aria-live', 'assertive');
    errorDiv.textContent = message;
    return errorDiv;
  }
}

// Semantic HTML utilities
export class SemanticHTML {
  // Ensure proper heading structure
  validateHeadingStructure(): boolean {
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    
    for (const heading of headings) {
      const level = parseInt(heading.tagName.charAt(1));
      if (level > previousLevel + 1) {
        console.warn(`Heading structure issue: ${heading.tagName} follows h${previousLevel}`);
        return false;
      }
      previousLevel = level;
    }
    
    return true;
  }

  // Add landmarks
  addLandmarks(): void {
    const main = document.querySelector('main');
    if (main) {
      main.setAttribute('role', 'main');
      main.setAttribute('aria-label', 'Contenido principal');
    }

    const nav = document.querySelector('nav');
    if (nav) {
      nav.setAttribute('role', 'navigation');
      nav.setAttribute('aria-label', 'Navegación principal');
    }
  }

  // Create accessible tables
  createAccessibleTable(table: HTMLTableElement, caption: string): void {
    const captionElement = document.createElement('caption');
    captionElement.textContent = caption;
    table.appendChild(captionElement);

    // Add scope to headers
    const headers = table.querySelectorAll('th');
    headers.forEach((header) => {
      if (header.parentElement?.tagName === 'THEAD') {
        header.setAttribute('scope', 'col');
      } else {
        header.setAttribute('scope', 'row');
      }
    });
  }
}

// Initialize accessibility features
export const initializeAccessibility = (): void => {
  const keyboardNav = new KeyboardNavigation();
  keyboardNav.createSkipLink();

  const semanticHTML = new SemanticHTML();
  semanticHTML.addLandmarks();
  semanticHTML.validateHeadingStructure();

  // Add global keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    // Alt + / for search focus
    if (e.altKey && e.key === '/') {
      e.preventDefault();
      const searchInput = document.querySelector('input[type="search"]') as HTMLInputElement;
      if (searchInput) {
        searchInput.focus();
      }
    }
  });
};

// Accessibility checker for forms
export class FormAccessibilityChecker {
  checkForm(form: HTMLFormElement): string[] {
    const issues: string[] = [];
    const inputs = form.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
      const element = input as HTMLInputElement;
      
      // Check for labels
      if (!element.id || !form.querySelector(`label[for="${element.id}"]`)) {
        issues.push(`Input ${element.name || element.type} missing label`);
      }

      // Check for required indicators
      if (element.required && !element.getAttribute('aria-required')) {
        issues.push(`Required field ${element.name} missing aria-required`);
      }

      // Check for error messages
      if (element.getAttribute('aria-invalid') === 'true' && !element.getAttribute('aria-describedby')) {
        issues.push(`Invalid field ${element.name} missing error description`);
      }
    });

    return issues;
  }
}