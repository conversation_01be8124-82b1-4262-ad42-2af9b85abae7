import React, { useState } from 'react';
import { BookOpen, Search, Star, FileText } from 'lucide-react';
import { SearchEngine } from '../components/knowledge-base/SearchEngine';
import { ResourceCatalog } from '../components/knowledge-base/ResourceCatalog';
import { TemplateLibrary } from '../components/knowledge-base/TemplateLibrary';
import { SEPCurriculumExplorer } from '../components/knowledge-base/SEPCurriculumExplorer';
import { SEPResource } from '../lib/sep-integration';

interface UserPreferences {
  grade: string;
  subject: string;
  type: string;
}

type TabType = 'search' | 'catalog' | 'templates' | 'curriculum' | 'recommendations';

export function KnowledgeBasePage() {
  const [activeTab, setActiveTab] = useState<TabType>('search');
  const [selectedResource, setSelectedResource] = useState<SEPResource | null>(null);
  const [userPreferences, setUserPreferences] = useState<UserPreferences>({
    grade: '',
    subject: '',
    type: ''
  });

  const tabs = [
    { id: 'search' as TabType, label: 'Búsqueda Inteligente', icon: Search },
    { id: 'catalog' as TabType, label: 'Catálogo SEP', icon: BookOpen },
    { id: 'curriculum' as TabType, label: 'Currículo Oficial', icon: BookOpen },
    { id: 'templates' as TabType, label: 'Plantillas', icon: FileText },
    { id: 'recommendations' as TabType, label: 'Recomendaciones', icon: Star },
  ];

  const handleResourceSelect = (resource: SEPResource) => {
    setSelectedResource(resource);
  };

  const handlePreferencesUpdate = (preferences: UserPreferences) => {
    setUserPreferences(preferences);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Base de Conocimiento SEP
          </h1>
          <p className="text-gray-600">
            Accede a recursos oficiales, plantillas y contenido educativo aprobado por la SEP
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6">
            {activeTab === 'search' && (
              <SearchEngine 
                onResourceSelect={handleResourceSelect}
                onPreferencesUpdate={handlePreferencesUpdate}
              />
            )}

            {activeTab === 'catalog' && (
              <ResourceCatalog
                onResourceSelect={handleResourceSelect}
                userPreferences={userPreferences}
              />
            )}

            {activeTab === 'curriculum' && (
              <SEPCurriculumExplorer />
            )}

            {activeTab === 'templates' && (
              <TemplateLibrary
                onTemplateSelect={handleResourceSelect}
                grade={userPreferences.grade}
                subject={userPreferences.subject}
              />
            )}

            {activeTab === 'recommendations' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-xl font-semibold mb-4">Recomendaciones Personalizadas</h2>
                  <p className="text-gray-600 mb-6">
                    Basadas en tus preferencias y actividad reciente
                  </p>
                </div>
                
                <ResourceCatalog
                  onResourceSelect={handleResourceSelect}
                  userPreferences={userPreferences}
                  isRecommendations={true}
                />
              </div>
            )}
          </div>
        </div>

        {/* Resource Preview Modal */}
        {selectedResource && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h2 className="text-2xl font-bold">{selectedResource.title}</h2>
                  <button
                    onClick={() => setSelectedResource(null)}
                    className="text-gray-400 hover:text-gray-600 text-2xl"
                  >
                    ×
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <span className="font-medium">Tipo:</span> {selectedResource.type}
                  </div>
                  <div>
                    <span className="font-medium">Categoría:</span> {selectedResource.category}
                  </div>
                  {selectedResource.grade && (
                    <div>
                      <span className="font-medium">Grado:</span> {selectedResource.grade}
                    </div>
                  )}
                  {selectedResource.subject && (
                    <div>
                      <span className="font-medium">Materia:</span> {selectedResource.subject}
                    </div>
                  )}
                  
                  <div>
                    <span className="font-medium">Contenido:</span>
                    <div className="mt-2 p-4 bg-gray-50 rounded">
                      <pre className="whitespace-pre-wrap text-sm">
                        {JSON.stringify(selectedResource.content, null, 2)}
                      </pre>
                    </div>
                  </div>

                  <div className="flex gap-4 pt-4">
                    <button
                      onClick={() => setSelectedResource(null)}
                      className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50"
                    >
                      Cerrar
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}