import React from 'react';
import { <PERSON> } from 'wouter';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BookOpen, Wand2, MessageSquare, CheckCircle2, BookTemplate as FileTemplate, TrendingUp, Calendar, Users } from 'lucide-react';
import { useAuthStore } from '@/stores/authStore';

export function HomePage() {
  const { user } = useAuthStore();

  const quickActions = [
    {
      title: 'Nueva Planeación',
      description: 'Crear una planeación didáctica completa con el wizard guiado',
      icon: BookOpen,
      href: '/planning',
      color: 'from-blue-500 to-blue-600',
    },
    {
      title: 'Chat con IA',
      description: 'Consulta al asistente pedagógico especializado en NEM',
      icon: MessageSquare,
      href: '/chat',
      color: 'from-green-500 to-green-600',
    },
    {
      title: '<PERSON><PERSON><PERSON> de Contenido',
      description: 'Genera actividades, proyectos y recursos automáticamente',
      icon: Wand2,
      href: '/generator',
      color: 'from-purple-500 to-purple-600',
    },
    {
      title: 'Validador NEM',
      description: 'Valida la coherencia de tus planeaciones con la NEM',
      icon: CheckCircle2,
      href: '/validator',
      color: 'from-orange-500 to-orange-600',
    },
  ];

  const stats = [
    { label: 'Planeaciones Creadas', value: '0', icon: BookOpen },
    { label: 'Consultas IA', value: '0', icon: MessageSquare },
    { label: 'Recursos Descargados', value: '0', icon: FileTemplate },
    { label: 'Días Activo', value: '1', icon: Calendar },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900">
          Bienvenido, {user?.firstName} 👋
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Tu asistente inteligente para crear planeaciones didácticas alineadas con la 
          <span className="font-semibold text-green-600"> Nueva Escuela Mexicana</span>
        </p>
        <div className="flex justify-center space-x-4">
          <Badge variant="outline" className="text-sm">
            {user?.school || 'Escuela Secundaria'}
          </Badge>
          <Badge variant="outline" className="text-sm">
            {user?.grade || 'Educación Secundaria'}
          </Badge>
          <Badge variant="outline" className="text-sm">
            {user?.subject || 'Materias Generales'}
          </Badge>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickActions.map((action, index) => {
          const Icon = action.icon;
          return (
            <Link key={index} href={action.href}>
              <Card className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105 group">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${action.color} flex items-center justify-center group-hover:scale-110 transition-transform`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                        {action.title}
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>

      {/* Stats Dashboard */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl flex items-center space-x-2">
            <TrendingUp className="w-5 h-5" />
            <span>Tu Progreso</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="text-center space-y-2">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                    <Icon className="w-6 h-6 text-gray-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-gray-600">{stat.label}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity & Getting Started */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Getting Started */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Primeros Pasos</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-sm">1</span>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-blue-900">
                    Explora el Chat con IA
                  </p>
                  <p className="text-xs text-blue-700">
                    Conoce al asistente pedagógico especializado en NEM
                  </p>
                </div>
                <Link href="/chat">
                  <Button size="sm" variant="outline">Ir</Button>
                </Link>
              </div>

              <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-bold text-sm">2</span>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-green-900">
                    Crea tu primera planeación
                  </p>
                  <p className="text-xs text-green-700">
                    Usa el wizard guiado de 6 pasos
                  </p>
                </div>
                <Link href="/planning">
                  <Button size="sm" variant="outline">Crear</Button>
                </Link>
              </div>

              <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 font-bold text-sm">3</span>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-purple-900">
                    Explora recursos SEP
                  </p>
                  <p className="text-xs text-purple-700">
                    Accede a contenidos oficiales y plantillas
                  </p>
                </div>
                <Link href="/resources">
                  <Button size="sm" variant="outline">Ver</Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* NEM Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Nueva Escuela Mexicana</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">4 Campos Formativos</h4>
                <div className="grid grid-cols-2 gap-2">
                  <Badge variant="outline" className="justify-center py-2">Lenguajes</Badge>
                  <Badge variant="outline" className="justify-center py-2">Saberes y Pensamiento</Badge>
                  <Badge variant="outline" className="justify-center py-2">Ética y Naturaleza</Badge>
                  <Badge variant="outline" className="justify-center py-2">Humano y Comunitario</Badge>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">7 Ejes Articuladores</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>• Inclusión</p>
                  <p>• Pensamiento Crítico</p>
                  <p>• Interculturalidad Crítica</p>
                  <p>• Igualdad de Género</p>
                  <p>• Vida Saludable</p>
                  <p>• Apropiación de Culturas</p>
                  <p>• Artes y Experiencias Estéticas</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Call to Action */}
      <Card className="bg-gradient-to-r from-blue-600 to-green-600 text-white">
        <CardContent className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">
            ¿Listo para crear tu primera planeación?
          </h2>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Nuestro wizard inteligente te guiará paso a paso para crear planeaciones 
            didácticas completamente alineadas con la Nueva Escuela Mexicana.
          </p>
          <Link href="/planning">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              <BookOpen className="w-5 h-5 mr-2" />
              Comenzar Planeación
            </Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  );
}