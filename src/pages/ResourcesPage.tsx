import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, TabsContent } from '@radix-ui/react-tabs';
import { BookOpen, FileText, Star, Video, Loader2, Search, Filter, Heart, Tag, Download, Calendar, Eye, ExternalLink, Image } from 'lucide-react';
import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { useToast } from '../hooks/useToast';
import { resourcesApi } from '../lib/api';

interface Resource {
  id: string;
  title: string;
  type: 'pda' | 'activity' | 'project' | 'rubric' | 'document' | 'video' | 'image';
  category: string;
  grade?: string;
  subject?: string;
  content: Record<string, unknown>;
  tags: string[];
  isOfficial: boolean;
  downloads: number;
  rating: number;
  averageRating: number;
  ratingCount: number;
  createdAt: string;
  author?: string;
}

const RESOURCE_TYPES = [
  { id: 'all', name: 'Todos', icon: BookOpen },
  { id: 'pda', name: 'PDAs', icon: FileText },
  { id: 'activity', name: 'Actividades', icon: Star },
  { id: 'project', name: 'Proyectos', icon: BookOpen },
  { id: 'rubric', name: 'Rúbricas', icon: FileText },
  { id: 'document', name: 'Documentos', icon: FileText },
  { id: 'video', name: 'Videos', icon: Video },
  { id: 'image', name: 'Imágenes', icon: Image },
];

const FORMATIVE_FIELDS = [
  'Todos',
  'Lenguajes',
  'Saberes y Pensamiento Científico',
  'Ética, Naturaleza y Sociedades',
  'De lo Humano y lo Comunitario',
];

const GRADES = [
  'Todos',
  '1° Secundaria',
  '2° Secundaria',
  '3° Secundaria',
];

const SUBJECTS = [
  'Todas',
  'Español',
  'Matemáticas',
  'Ciencias Naturales',
  'Historia',
  'Geografía',
  'Formación Cívica y Ética',
  'Educación Física',
  'Artes',
  'Inglés',
  'Tecnología',
];

export function ResourcesPage() {
  const [resources, setResources] = useState<Resource[]>([]);
  const [filteredResources, setFilteredResources] = useState<Resource[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('Todos');
  const [selectedGrade, setSelectedGrade] = useState('Todos');
  const [selectedSubject, setSelectedSubject] = useState('Todas');
  const [showFilters, setShowFilters] = useState(false);
  const [favorites, setFavorites] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingFavorites, setLoadingFavorites] = useState(false);
  const { toast } = useToast();

  const loadResources = useCallback(async () => {
    try {
      setLoading(true);
      const response = await resourcesApi.getResources();
      setResources(response.data.resources || []);
    } catch (error) {
      console.error('Error loading resources:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No se pudieron cargar los recursos',
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const loadFavorites = useCallback(async () => {
    try {
      setLoadingFavorites(true);
      const response = await resourcesApi.getFavorites();
      const favoriteIds = response.data.resources.map((r: Resource) => r.id);
      setFavorites(favoriteIds);
    } catch (error) {
      console.error('Error loading favorites:', error);
    } finally {
      setLoadingFavorites(false);
    }
  }, []);

  // Load resources and favorites
  useEffect(() => {
    loadResources();
    loadFavorites();
  }, [loadResources, loadFavorites]);

  // Filter resources
  useEffect(() => {
    let filtered = resources;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(resource =>
        resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Type filter
    if (selectedType !== 'all') {
      filtered = filtered.filter(resource => resource.type === selectedType);
    }

    // Category filter
    if (selectedCategory !== 'Todos') {
      filtered = filtered.filter(resource => resource.category === selectedCategory);
    }

    // Grade filter
    if (selectedGrade !== 'Todos') {
      filtered = filtered.filter(resource => resource.grade === selectedGrade);
    }

    // Subject filter
    if (selectedSubject !== 'Todas') {
      filtered = filtered.filter(resource => resource.subject === selectedSubject);
    }

    setFilteredResources(filtered);
  }, [resources, searchTerm, selectedType, selectedCategory, selectedGrade, selectedSubject]);

  const handleDownload = async (resource: Resource) => {
    try {
      await resourcesApi.trackDownload(resource.id);
      
      // Update local state
      setResources(prev => prev.map(r => 
        r.id === resource.id 
          ? { ...r, downloads: r.downloads + 1 }
          : r
      ));
      
      toast({
        title: 'Descarga iniciada',
        description: `Descargando: ${resource.title}`,
      });
    } catch (error) {
      console.error('Error downloading resource:', error);
      toast({
        variant: 'destructive',
        title: 'Error en descarga',
        description: 'No se pudo descargar el recurso',
      });
    }
  };

  const toggleFavorite = async (resourceId: string) => {
    try {
      await resourcesApi.toggleFavorite(resourceId);
      
      if (favorites.includes(resourceId)) {
        setFavorites(prev => prev.filter(id => id !== resourceId));
        toast({
          title: 'Eliminado de favoritos',
          description: 'El recurso ha sido eliminado de tus favoritos',
        });
      } else {
        setFavorites(prev => [...prev, resourceId]);
        toast({
          title: 'Agregado a favoritos',
          description: 'El recurso ha sido agregado a tus favoritos',
        });
      }
      
      // Reload favorites to get updated data
      loadFavorites();
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No se pudo actualizar favoritos',
      });
    }
  };

  const getTypeIcon = (type: string) => {
    const typeConfig = RESOURCE_TYPES.find(t => t.id === type);
    const Icon = typeConfig?.icon || FileText;
    return <Icon className="w-4 h-4" />;
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedType('all');
    setSelectedCategory('Todos');
    setSelectedGrade('Todos');
    setSelectedSubject('Todas');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">
          Biblioteca de Recursos NEM
        </h1>
        <p className="text-gray-600">
          Accede a recursos oficiales SEP y contenido de la comunidad educativa
        </p>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="flex space-x-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Buscar recursos, PDAs, actividades..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2"
              >
                <Filter className="w-4 h-4" />
                <span>Filtros</span>
              </Button>
              {(searchTerm || selectedType !== 'all' || selectedCategory !== 'Todos') && (
                <Button variant="ghost" onClick={clearFilters}>
                  Limpiar
                </Button>
              )}
            </div>

            {/* Filters */}
            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo de Recurso
                  </label>
                  <select
                    value={selectedType}
                    onChange={(e) => setSelectedType(e.target.value)}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                  >
                    {RESOURCE_TYPES.map(type => (
                      <option key={type.id} value={type.id}>
                        {type.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Campo Formativo
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                  >
                    {FORMATIVE_FIELDS.map(field => (
                      <option key={field} value={field}>
                        {field}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Grado
                  </label>
                  <select
                    value={selectedGrade}
                    onChange={(e) => setSelectedGrade(e.target.value)}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                  >
                    {GRADES.map(grade => (
                      <option key={grade} value={grade}>
                        {grade}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Materia
                  </label>
                  <select
                    value={selectedSubject}
                    onChange={(e) => setSelectedSubject(e.target.value)}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                  >
                    {SUBJECTS.map(subject => (
                      <option key={subject} value={subject}>
                        {subject}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Resource Types Tabs */}
      <Tabs value={selectedType} onValueChange={setSelectedType}>
        <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8">
          {RESOURCE_TYPES.map(type => {
            const Icon = type.icon;
            return (
              <TabsTrigger key={type.id} value={type.id} className="flex items-center space-x-1">
                <Icon className="w-4 h-4" />
                <span className="hidden sm:inline">{type.name}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value={selectedType} className="mt-6">
          {/* Results Summary */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <h2 className="text-lg font-semibold text-gray-900">
                {filteredResources.length} recursos encontrados
              </h2>
              {searchTerm && (
                <Badge variant="outline">
                  Búsqueda: "{searchTerm}"
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Ordenar por:</span>
              <select
                className="rounded-md border border-gray-300 px-3 py-1 text-sm"
                onChange={(e) => {
                  // Handle sorting - TODO: implement sorting logic
                  console.log('Sort by:', e.target.value);
                }}
              >
                <option value="createdAt-desc">Más recientes</option>
                <option value="downloads-desc">Más descargados</option>
                <option value="rating-desc">Mejor valorados</option>
                <option value="title-asc">Alfabético</option>
              </select>
            </div>
          </div>

          {/* Resources Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredResources.map(resource => (
              <Card key={resource.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(resource.type)}
                      <Badge variant={resource.isOfficial ? 'default' : 'secondary'}>
                        {resource.isOfficial ? 'SEP Oficial' : 'Comunidad'}
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleFavorite(resource.id)}
                      className={favorites.includes(resource.id) ? 'text-red-600' : 'text-gray-400'}
                      disabled={loadingFavorites}
                    >
                      <Heart className="w-4 h-4" />
                    </Button>
                  </div>
                  <CardTitle className="text-lg leading-tight">
                    {resource.title}
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Resource Info */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <Tag className="w-3 h-3" />
                        <span>{resource.category}</span>
                      </div>
                      {resource.grade && (
                        <div className="flex items-center space-x-1">
                          <BookOpen className="w-3 h-3" />
                          <span>{resource.grade}</span>
                        </div>
                      )}
                    </div>
                    
                    {resource.subject && (
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Materia:</span> {resource.subject}
                      </div>
                    )}
                  </div>

                  {/* Rating */}
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-4 h-4 ${star <= resource.averageRating ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">
                      {resource.averageRating} ({resource.ratingCount})
                    </span>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1">
                    {resource.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {resource.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{resource.tags.length - 3}
                      </Badge>
                    )}
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Download className="w-3 h-3" />
                        <span>{resource.downloads.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{new Date(resource.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Button
                      size="sm"
                      onClick={() => handleDownload(resource)}
                      className="flex-1 flex items-center space-x-1"
                    >
                      <Download className="w-3 h-3" />
                      <span>Descargar</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="flex items-center space-x-1"
                    >
                      <Eye className="w-3 h-3" />
                      <span>Ver</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="flex items-center space-x-1"
                    >
                      <ExternalLink className="w-3 h-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Empty State */}
          {filteredResources.length === 0 && (
            <Card className="border-dashed">
              <CardContent className="p-12 text-center">
                <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No se encontraron recursos
                </h3>
                <p className="text-gray-600 mb-4">
                  Intenta ajustar los filtros o términos de búsqueda
                </p>
                <Button onClick={clearFilters}>
                  Limpiar Filtros
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Featured Resources */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Recursos Destacados</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">📚 Programas Sintéticos 2024</h4>
              <p className="text-sm text-blue-700 mb-3">
                Documentos oficiales actualizados para todos los grados
              </p>
              <Button size="sm" variant="outline">Ver Colección</Button>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2">🎯 PDAs por Campo Formativo</h4>
              <p className="text-sm text-green-700 mb-3">
                Procesos de desarrollo organizados por campo
              </p>
              <Button size="sm" variant="outline">Explorar</Button>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-medium text-purple-900 mb-2">⭐ Más Valorados</h4>
              <p className="text-sm text-purple-700 mb-3">
                Recursos mejor calificados por la comunidad
              </p>
              <Button size="sm" variant="outline">Ver Top</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
