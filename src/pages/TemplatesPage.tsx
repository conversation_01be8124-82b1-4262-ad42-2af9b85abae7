import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../components/ui/tabs';
import { 
  Search, 
  Plus, 
  Download, 
  Star, 
  BookTemplate as FileTemplate, 
  Copy,
  Eye,
  Calendar,
  User,
  TrendingUp
} from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { useAuthStore } from '../stores/authStore';

interface Template {
  id: string;
  name: string;
  description: string;
  type: 'planning' | 'activity' | 'project' | 'evaluation';
  formativeField?: string;
  grade?: string;
  subject?: string;
  template: {
    structure?: string;
    fields?: string[];
    validation?: string;
    sections?: string[];
    methodology?: string;
    duration?: string;
    phases?: string[];
    scope?: string[];
    impact_measurement?: boolean;
    criteria_types?: string[];
    levels?: string[];
    customizable?: boolean;
    disciplines?: string[];
    integration_level?: string;
    project_based?: boolean;
  };
  isPublic: boolean;
  usage: number;
  createdAt: string;
  author: string;
  isOfficial: boolean;
  rating?: number;
}

const TEMPLATE_TYPES = [
  { id: 'all', name: 'Todas', icon: FileTemplate },
  { id: 'planning', name: 'Planeaciones', icon: FileTemplate },
  { id: 'activity', name: 'Actividades', icon: Star },
  { id: 'project', name: 'Proyectos', icon: FileTemplate },
  { id: 'evaluation', name: 'Evaluación', icon: FileTemplate },
];

// Sample templates data
const SAMPLE_TEMPLATES: Template[] = [
  {
    id: '1',
    name: 'Planeación Básica NEM - Secundaria',
    description: 'Plantilla completa para planeaciones didácticas alineadas con la Nueva Escuela Mexicana',
    type: 'planning',
    formativeField: 'Lenguajes',
    grade: '1° Secundaria',
    subject: 'Español',
    template: {
      structure: 'wizard_6_steps',
      fields: ['general_info', 'formative_field', 'pda', 'activities', 'evaluation', 'review'],
      validation: 'nem_aligned'
    },
    isPublic: true,
    usage: 2450,
    createdAt: '2024-01-15',
    author: 'SEP Oficial',
    isOfficial: true,
    rating: 96
  },
  {
    id: '2',
    name: 'Actividad Socioconstructivista',
    description: 'Plantilla para diseñar actividades con metodología socioconstructivista',
    type: 'activity',
    formativeField: 'Saberes y Pensamiento Científico',
    template: {
      sections: ['objetivos', 'materiales', 'desarrollo', 'evaluacion', 'reflexion'],
      methodology: 'socioconstructivista',
      duration: 'flexible'
    },
    isPublic: true,
    usage: 1890,
    createdAt: '2024-02-01',
    author: 'Prof. Ana Martínez',
    isOfficial: false,
    rating: 92
  },
  {
    id: '3',
    name: 'Proyecto Comunitario NEM',
    description: 'Estructura para proyectos de aula, escuela y comunidad',
    type: 'project',
    formativeField: 'Ética, Naturaleza y Sociedades',
    template: {
      phases: ['diagnostico', 'planificacion', 'implementacion', 'evaluacion'],
      scope: ['aula', 'escuela', 'comunidad'],
      impact_measurement: true
    },
    isPublic: true,
    usage: 1560,
    createdAt: '2024-01-20',
    author: 'SEP Oficial',
    isOfficial: true,
    rating: 94
  },
  {
    id: '4',
    name: 'Rúbrica Analítica Personalizable',
    description: 'Plantilla de rúbrica adaptable a diferentes competencias y criterios',
    type: 'evaluation',
    template: {
      criteria_types: ['conocimientos', 'habilidades', 'actitudes'],
      levels: ['excelente', 'satisfactorio', 'en_desarrollo', 'requiere_apoyo'],
      customizable: true
    },
    isPublic: true,
    usage: 3200,
    createdAt: '2024-02-10',
    author: 'Prof. Carlos Ruiz',
    isOfficial: false,
    rating: 89
  },
  {
    id: '5',
    name: 'Planeación STEAM Integrada',
    description: 'Plantilla para proyectos STEAM con enfoque interdisciplinario',
    type: 'planning',
    formativeField: 'Saberes y Pensamiento Científico',
    grade: '3° Secundaria',
    template: {
      disciplines: ['ciencias', 'tecnologia', 'ingenieria', 'artes', 'matematicas'],
      integration_level: 'high',
      project_based: true
    },
    isPublic: true,
    usage: 980,
    createdAt: '2024-02-15',
    author: 'Equipo STEAM México',
    isOfficial: false,
    rating: 91
  }
];

export function TemplatesPage() {
  const [templates, setTemplates] = useState<Template[]>(SAMPLE_TEMPLATES);
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>(SAMPLE_TEMPLATES);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [showMyTemplates, setShowMyTemplates] = useState(false);
  const { user } = useAuthStore();
  const { toast } = useToast();

  // Filter templates
  React.useEffect(() => {
    let filtered = templates;

    if (searchTerm) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedType !== 'all') {
      filtered = filtered.filter(template => template.type === selectedType);
    }

    if (showMyTemplates) {
      filtered = filtered.filter(template => template.author === `${user?.firstName} ${user?.lastName}`);
    }

    setFilteredTemplates(filtered);
  }, [templates, searchTerm, selectedType, showMyTemplates, user]);

  const handleUseTemplate = (template: Template) => {
    toast({
      title: 'Plantilla aplicada',
      description: `Se ha aplicado la plantilla: ${template.name}`,
    });

    // Update usage count
    setTemplates(prev => prev.map(t => 
      t.id === template.id 
        ? { ...t, usage: t.usage + 1 }
        : t
    ));
  };

  const handleCopyTemplate = (_template: Template) => {
    toast({
      title: 'Plantilla copiada',
      description: 'La plantilla se ha copiado a tu biblioteca personal',
    });
  };

  const getTypeIcon = (type: string) => {
    const typeConfig = TEMPLATE_TYPES.find(t => t.id === type);
    const Icon = typeConfig?.icon || FileTemplate;
    return <Icon className="w-4 h-4" />;
  };

  const getTypeColor = (type: string) => {
    const colors = {
      planning: 'bg-blue-100 text-blue-800',
      activity: 'bg-green-100 text-green-800',
      project: 'bg-purple-100 text-purple-800',
      evaluation: 'bg-orange-100 text-orange-800',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">
          Biblioteca de Plantillas
        </h1>
        <p className="text-gray-600">
          Acelera tu trabajo con plantillas prediseñadas y personalizables
        </p>
      </div>

      {/* Search and Actions */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Buscar plantillas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex space-x-2">
              <Button
                variant={showMyTemplates ? 'default' : 'outline'}
                onClick={() => setShowMyTemplates(!showMyTemplates)}
                className="flex items-center space-x-2"
              >
                <User className="w-4 h-4" />
                <span>Mis Plantillas</span>
              </Button>
              <Button className="flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>Nueva Plantilla</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Template Types Tabs */}
      <Tabs value={selectedType} onValueChange={setSelectedType}>
        <TabsList className="grid w-full grid-cols-5">
          {TEMPLATE_TYPES.map(type => {
            const Icon = type.icon;
            return (
              <TabsTrigger key={type.id} value={type.id} className="flex items-center space-x-1">
                <Icon className="w-4 h-4" />
                <span>{type.name}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value={selectedType} className="mt-6">
          {/* Results Summary */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">
              {filteredTemplates.length} plantillas disponibles
            </h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Ordenar por:</span>
              <select className="rounded-md border border-gray-300 px-3 py-1 text-sm">
                <option>Más usadas</option>
                <option>Mejor valoradas</option>
                <option>Más recientes</option>
                <option>Alfabético</option>
              </select>
            </div>
          </div>

          {/* Templates Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map(template => (
              <Card key={template.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(template.type)}
                      <Badge className={getTypeColor(template.type)}>
                        {TEMPLATE_TYPES.find(t => t.id === template.type)?.name}
                      </Badge>
                      {template.isOfficial && (
                        <Badge variant="default">SEP</Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-1 text-sm text-gray-600">
                      <TrendingUp className="w-3 h-3" />
                      <span>{template.usage}</span>
                    </div>
                  </div>
                  <CardTitle className="text-lg leading-tight">
                    {template.name}
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <p className="text-sm text-gray-600 line-clamp-3">
                    {template.description}
                  </p>

                  {/* Template Details */}
                  <div className="space-y-2">
                    {template.formativeField && (
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Campo:</span>
                        <span className="ml-1 text-gray-600">{template.formativeField}</span>
                      </div>
                    )}
                    {template.grade && (
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Grado:</span>
                        <span className="ml-1 text-gray-600">{template.grade}</span>
                      </div>
                    )}
                    {template.subject && (
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Materia:</span>
                        <span className="ml-1 text-gray-600">{template.subject}</span>
                      </div>
                    )}
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Download className="w-3 h-3" />
                        <span>{template.usage.toLocaleString()}</span>
                      </div>
                      {template.rating && (
                        <div className="flex items-center space-x-1">
                          <Star className="w-3 h-3 text-yellow-500" />
                          <span>{template.rating}%</span>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{new Date(template.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {/* Author */}
                  <div className="flex items-center space-x-1 text-sm text-gray-600">
                    <User className="w-3 h-3" />
                    <span>{template.author}</span>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Button
                      size="sm"
                      onClick={() => handleUseTemplate(template)}
                      className="flex-1 flex items-center space-x-1"
                    >
                      <Download className="w-3 h-3" />
                      <span>Usar</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleCopyTemplate(template)}
                      className="flex items-center space-x-1"
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="flex items-center space-x-1"
                    >
                      <Eye className="w-3 h-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Empty State */}
          {filteredTemplates.length === 0 && (
            <Card className="border-dashed">
              <CardContent className="p-12 text-center">
                <FileTemplate className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {showMyTemplates ? 'No tienes plantillas' : 'No se encontraron plantillas'}
                </h3>
                <p className="text-gray-600 mb-4">
                  {showMyTemplates 
                    ? 'Crea tu primera plantilla personalizada'
                    : 'Intenta ajustar los filtros o términos de búsqueda'
                  }
                </p>
                <Button>
                  {showMyTemplates ? 'Crear Plantilla' : 'Limpiar Filtros'}
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Featured Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Plantillas Destacadas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">📋 Planeaciones NEM</h4>
              <p className="text-sm text-blue-700 mb-3">
                Plantillas oficiales para todos los campos formativos
              </p>
              <Button size="sm" variant="outline">Ver Colección</Button>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2">⭐ Más Populares</h4>
              <p className="text-sm text-green-700 mb-3">
                Las plantillas más utilizadas por la comunidad
              </p>
              <Button size="sm" variant="outline">Explorar</Button>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-medium text-purple-900 mb-2">🆕 Nuevas Plantillas</h4>
              <p className="text-sm text-purple-700 mb-3">
                Últimas incorporaciones a la biblioteca
              </p>
              <Button size="sm" variant="outline">Ver Nuevas</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Create Template CTA */}
      <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <CardContent className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">
            ¿Tienes una plantilla exitosa?
          </h2>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Comparte tus plantillas con la comunidad educativa y ayuda a otros docentes 
            a crear planeaciones de calidad más rápidamente.
          </p>
          <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
            <Plus className="w-5 h-5 mr-2" />
            Crear Nueva Plantilla
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}