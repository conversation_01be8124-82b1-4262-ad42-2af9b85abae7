import { create } from "zustand";
import { persist } from "zustand/middleware";

export interface PlanningStep1 {
  title: string;
  subject: string;
  grade: string;
  school: string;
  startDate: string;
  endDate: string;
  duration: number;
}

export interface PlanningStep2 {
  formativeField: string;
  articulatingAxes: string[];
}

export interface PlanningStep3 {
  pda: string;
  description: string;
  objectives: string[];
}

export interface PlanningStep4 {
  activities: Array<{
    id: string;
    name: string;
    description: string;
    duration: number;
    type: string;
    materials: string[];
  }>;
}

export interface PlanningStep5 {
  evaluation: {
    instruments: string[];
    criteria: string[];
    evidences: string[];
  };
}

export interface PlanningStep6 {
  resources: string[];
  observations: string;
}

export interface WizardData {
  step1: PlanningStep1;
  step2: PlanningStep2;
  step3: PlanningStep3;
  step4: PlanningStep4;
  step5: PlanningStep5;
  step6: PlanningStep6;
}

interface PlanningWizardState {
  currentStep: number;
  wizardData: Partial<WizardData>;
  isValid: Record<number, boolean>;
  setCurrentStep: (step: number) => void;
  updateStepData: <T extends keyof WizardData>(
    step: T,
    data: Partial<WizardData[T]>
  ) => void;
  setStepValid: (step: number, valid: boolean) => void;
  resetWizard: () => void;
  canProceedToStep: (step: number) => boolean;
}

const initialData: Partial<WizardData> = {
  step1: {
    title: "",
    subject: "",
    grade: "",
    school: "",
    startDate: "",
    endDate: "",
    duration: 50,
  },
  step2: {
    formativeField: "",
    articulatingAxes: [],
  },
  step3: {
    pda: "",
    description: "",
    objectives: [],
  },
  step4: {
    activities: [],
  },
  step5: {
    evaluation: {
      instruments: [],
      criteria: [],
      evidences: [],
    },
  },
  step6: {
    resources: [],
    observations: "",
  },
};

export const usePlanningWizardStore = create<PlanningWizardState>()(
  persist(
    (set, get) => ({
      currentStep: 1,
      wizardData: initialData,
      isValid: {},

      setCurrentStep: (step) => set({ currentStep: step }),

      updateStepData: (step, data) =>
        set((state) => ({
          wizardData: {
            ...state.wizardData,
            [step]: {
              ...state.wizardData[step],
              ...data,
            },
          },
        })),

      setStepValid: (step, valid) =>
        set((state) => ({
          isValid: {
            ...state.isValid,
            [step]: valid,
          },
        })),

      resetWizard: () =>
        set({
          currentStep: 1,
          wizardData: initialData,
          isValid: {},
        }),

      canProceedToStep: (step) => {
        const { isValid } = get();
        // Check if all previous steps are valid
        for (let i = 1; i < step; i++) {
          if (!isValid[i]) return false;
        }
        return true;
      },
    }),
    {
      name: "planning-wizard-storage",
    }
  )
);
