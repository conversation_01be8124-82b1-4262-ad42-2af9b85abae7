import React, { useState, useEffect } from 'react';
import { BookOpen, Download, Eye, Grid, List, Search } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { sepIntegration, SEPResource } from '../../lib/sep-integration';

interface ResourceCatalogProps {
  onResourceSelect?: (resource: SEPResource) => void;
  userPreferences?: {
    grade?: string;
    subject?: string;
    type?: string;
  };
  isRecommendations?: boolean;
}

export const ResourceCatalog: React.FC<ResourceCatalogProps> = ({
  onResourceSelect,
  userPreferences = {},
  isRecommendations = false
}) => {
  const [resources, setResources] = useState<SEPResource[]>([]);
  const [filteredResources, setFilteredResources] = useState<SEPResource[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filters, setFilters] = useState({
    type: userPreferences.type || 'all',
    grade: userPreferences.grade || 'all',
    subject: userPreferences.subject || 'all',
    search: ''
  });

  useEffect(() => {
    loadResources();
  }, [userPreferences]);

  useEffect(() => {
    applyFilters();
  }, [resources, filters]);

  const loadResources = async () => {
    try {
      setLoading(true);
      
      if (isRecommendations) {
        // Use recommendation engine for personalized recommendations
        const { recommendationEngine } = await import('../../lib/recommendation-engine');
        const recommendations = await recommendationEngine.getPersonalizedRecommendations({
          grade: userPreferences.grade,
          subject: userPreferences.subject,
          type: userPreferences.type
        });
        setResources(recommendations.map(r => r.resource));
      } else {
        // Use regular catalog
        const result = await sepIntegration.getResources({
          type: filters.type !== 'all' ? filters.type : undefined,
          grade: filters.grade !== 'all' ? filters.grade : undefined,
          subject: filters.subject !== 'all' ? filters.subject : undefined,
          limit: 50
        });
        setResources(result.resources);
      }
    } catch (error) {
      console.error('Error loading resources:', error);
      // Set empty array on error to prevent UI crash
      setResources([]);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...resources];

    if (filters.search) {
      filtered = filtered.filter(r => 
        r.title.toLowerCase().includes(filters.search.toLowerCase()) ||
        r.category.toLowerCase().includes(filters.search.toLowerCase()) ||
        (r.tags && r.tags.some(tag => tag.toLowerCase().includes(filters.search.toLowerCase())))
      );
    }

    if (filters.type !== 'all') {
      filtered = filtered.filter(r => r.type === filters.type);
    }

    if (filters.grade !== 'all') {
      filtered = filtered.filter(r => r.grade === filters.grade);
    }

    if (filters.subject !== 'all') {
      filtered = filtered.filter(r => r.subject === filters.subject);
    }

    setFilteredResources(filtered);
  };

  const handleDownload = async (resource: SEPResource) => {
    try {
      await sepIntegration.incrementDownload(resource.id);
      // In a real app, this would trigger actual file download
      console.log('Download started for:', resource.title);
    } catch (error) {
      console.error('Error downloading resource:', error);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'planeacion': return <BookOpen className="h-5 w-5" />;
      case 'actividad': return <BookOpen className="h-5 w-5" />;
      case 'proyecto': return <BookOpen className="h-5 w-5" />;
      case 'rubrica': return <BookOpen className="h-5 w-5" />;
      default: return <BookOpen className="h-5 w-5" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'planeacion': return 'bg-blue-100 text-blue-800';
      case 'actividad': return 'bg-green-100 text-green-800';
      case 'proyecto': return 'bg-purple-100 text-purple-800';
      case 'rubrica': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getGradeOptions = () => {
    return ['1°', '2°', '3°', '4°', '5°', '6°'];
  };

  const getSubjectOptions = () => {
    return [
      'Lenguaje y Comunicación',
      'Matemáticas',
      'Ciencias Naturales',
      'Ciencias Sociales',
      'Artes',
      'Educación Física',
      'Formación Cívica y Ética'
    ];
  };

  const getTypeOptions = () => {
    return ['planeacion', 'actividad', 'proyecto', 'rubrica', 'evaluacion'];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">
            {isRecommendations ? 'Recomendaciones Personalizadas' : 'Catálogo de Recursos SEP'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Buscar recursos..."
                  value={filters.search}
                  onChange={(e) => setFilters({...filters, search: e.target.value})}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Select value={filters.type} onValueChange={(value) => setFilters({...filters, type: value})}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  {getTypeOptions().map(type => (
                    <SelectItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={filters.grade} onValueChange={(value) => setFilters({...filters, grade: value})}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Grado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  {getGradeOptions().map(grade => (
                    <SelectItem key={grade} value={grade}>{grade}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={filters.subject} onValueChange={(value) => setFilters({...filters, subject: value})}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Materia" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas</SelectItem>
                  {getSubjectOptions().map(subject => (
                    <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="flex gap-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="icon"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="icon"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          <div className="text-sm text-gray-600 mb-4">
            Mostrando {filteredResources.length} de {resources.length} recursos
          </div>

          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredResources.map((resource) => (
                <Card key={resource.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="aspect-video bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                      <BookOpen className="h-12 w-12 text-gray-400" />
                    </div>
                    <CardTitle className="text-lg">{resource.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-3">{resource.category}</p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Tipo:</span>
                        <Badge className={getTypeColor(resource.type)}>
                          {getTypeIcon(resource.type)}
                          <span className="ml-1">{resource.type}</span>
                        </Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Grado:</span>
                        <span>{resource.grade || 'Todos'}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Materia:</span>
                        <span>{resource.subject || 'General'}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Descargas:</span>
                        <span>{resource.downloads}</span>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        className="flex-1" 
                        onClick={() => {
                          if (onResourceSelect) {
                            onResourceSelect(resource);
                          }
                        }}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Ver
                      </Button>
                      <Button 
                        size="sm" 
                        className="flex-1" 
                        onClick={() => handleDownload(resource)}
                      >
                        <Download className="h-4 w-4 mr-1" />
                        Descargar
                      </Button>
                    </div>

                    {resource.isOfficial && (
                      <Badge className="w-full mt-3 bg-green-100 text-green-800">
                        Recurso Oficial SEP
                      </Badge>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredResources.map((resource) => (
                <Card key={resource.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4">
                      <div className="w-24 h-32 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <BookOpen className="h-8 w-8 text-gray-400" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="text-lg font-semibold">{resource.title}</h3>
                            <p className="text-sm text-gray-600 mt-1">{resource.category}</p>
                            
                            <div className="flex gap-2 mt-2">
                              <Badge className={getTypeColor(resource.type)}>
                                {getTypeIcon(resource.type)}
                                <span className="ml-1">{resource.type}</span>
                              </Badge>
                              {resource.grade && <Badge variant="outline">{resource.grade}</Badge>}
                              {resource.subject && <Badge variant="outline">{resource.subject}</Badge>}
                              {resource.isOfficial && (
                                <Badge className="bg-green-100 text-green-800">Oficial SEP</Badge>
                              )}
                            </div>

                            <div className="flex gap-4 mt-2 text-sm text-gray-500">
                              <span>Descargas: {resource.downloads}</span>
                              <span>Calificación: {resource.rating}/5</span>
                            </div>
                          </div>

                          <div className="flex gap-2 ml-4">
                            <Button 
                              size="sm"
                              onClick={() => {
                                if (onResourceSelect) {
                                  onResourceSelect(resource);
                                }
                              }}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              Ver
                            </Button>
                            <Button size="sm" onClick={() => handleDownload(resource)}>
                              <Download className="h-4 w-4 mr-1" />
                              Descargar
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};