import React, { useState, useEffect } from 'react';
import { Search, Download, Eye, Star, Calendar, User } from 'lucide-react';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';

interface SearchFilters {
  grade: string;
  subject: string;
  resourceType: string;
}

interface SearchResult {
  id: string;
  title: string;
  description: string;
  type: string;
  grade: string;
  subject: string;
  downloadUrl: string;
  previewUrl?: string;
  author: string;
  date: string;
  rating: number;
  tags: string[];
  official: boolean;
}

export const SearchEngine: React.FC = () => {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({
    grade: '',
    subject: '',
    resourceType: ''
  });
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [recommendations, setRecommendations] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  // Mock data
  const mockResults: SearchResult[] = [
    {
      id: '1',
      title: 'Matemáticas 3er Grado - Libro de Texto',
      description: 'Libro de texto oficial de matemáticas para tercer grado de primaria, incluye ejercicios y actividades prácticas.',
      type: 'libro',
      grade: 'primaria',
      subject: 'matematicas',
      downloadUrl: '/downloads/matematicas-3ro.pdf',
      author: 'SEP',
      date: '2024-01-15',
      rating: 4.8,
      tags: ['primaria', 'matemáticas', 'libro-texto', 'oficial'],
      official: true
    },
    {
      id: '2',
      title: 'Guía Didáctica - Ciencias Naturales 5to Grado',
      description: 'Guía completa para docentes con estrategias de enseñanza y actividades complementarias.',
      type: 'guia',
      grade: 'primaria',
      subject: 'ciencias',
      downloadUrl: '/downloads/ciencias-5to-guia.pdf',
      author: 'SEP',
      date: '2024-02-20',
      rating: 4.6,
      tags: ['primaria', 'ciencias', 'guia-docente', 'oficial'],
      official: true
    },
    {
      id: '3',
      title: 'Actividades de Lenguaje - Lectoescritura',
      description: 'Conjunto de actividades interactivas para desarrollar habilidades de lectoescritura en primer grado.',
      type: 'actividad',
      grade: 'primaria',
      subject: 'lenguaje',
      downloadUrl: '/downloads/lenguaje-actividades.pdf',
      author: 'SEP',
      date: '2024-03-10',
      rating: 4.7,
      tags: ['primaria', 'lenguaje', 'actividades', 'oficial'],
      official: true
    }
  ];

  useEffect(() => {
    loadRecommendations();
    loadRecentSearches();
  }, []);

  const loadRecommendations = async () => {
    // Mock recommendations
    setRecommendations(mockResults.slice(0, 2));
  };

  const loadRecentSearches = () => {
    const stored = localStorage.getItem('recentSEPQueries');
    if (stored) {
      setRecentSearches(JSON.parse(stored));
    }
  };

  const handleSearch = async () => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      // Save to recent searches
      const updatedSearches = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5);
      setRecentSearches(updatedSearches);
      localStorage.setItem('recentSEPQueries', JSON.stringify(updatedSearches));

      // Filter mock results based on query and filters
      let filtered = mockResults.filter(r => 
        r.title.toLowerCase().includes(query.toLowerCase()) ||
        r.description.toLowerCase().includes(query.toLowerCase())
      );

      if (filters.grade) {
        filtered = filtered.filter(r => r.grade === filters.grade);
      }
      if (filters.subject) {
        filtered = filtered.filter(r => r.subject === filters.subject);
      }
      if (filters.resourceType) {
        filtered = filtered.filter(r => r.type === filters.resourceType);
      }

      setResults(filtered);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleQuickSearch = (term: string) => {
    setQuery(term);
    handleSearch();
  };

  const handleDownload = (result: SearchResult) => {
    console.log('Downloading:', result.title);
    // In real implementation, this would trigger actual download
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Búsqueda Inteligente</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Buscar recursos educativos por palabras clave..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pl-10 pr-4 py-2 w-full"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Select value={filters.grade} onValueChange={(value) => setFilters({...filters, grade: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Grado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="preescolar">Preescolar</SelectItem>
                <SelectItem value="primaria">Primaria</SelectItem>
                <SelectItem value="secundaria">Secundaria</SelectItem>
                <SelectItem value="bachillerato">Bachillerato</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.subject} onValueChange={(value) => setFilters({...filters, subject: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Materia" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="matematicas">Matemáticas</SelectItem>
                <SelectItem value="lenguaje">Lenguaje y Comunicación</SelectItem>
                <SelectItem value="ciencias">Ciencias</SelectItem>
                <SelectItem value="historia">Historia</SelectItem>
                <SelectItem value="geografia">Geografía</SelectItem>
                <SelectItem value="arte">Arte</SelectItem>
                <SelectItem value="educacion-fisica">Educación Física</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.resourceType} onValueChange={(value) => setFilters({...filters, resourceType: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="libro">Libro de Texto</SelectItem>
                <SelectItem value="guia">Guía Didáctica</SelectItem>
                <SelectItem value="actividad">Actividad</SelectItem>
                <SelectItem value="evaluacion">Evaluación</SelectItem>
                <SelectItem value="video">Video</SelectItem>
                <SelectItem value="presentacion">Presentación</SelectItem>
              </SelectContent>
            </Select>

            <Button onClick={handleSearch} disabled={loading} className="w-full">
              <Search className="h-4 w-4 mr-2" />
              {loading ? 'Buscando...' : 'Buscar'}
            </Button>
          </div>

          {recentSearches.length > 0 && (
            <div className="flex flex-wrap gap-2">
              <span className="text-sm text-gray-600">Búsquedas recientes:</span>
              {recentSearches.map((search, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="cursor-pointer hover:bg-gray-100"
                  onClick={() => handleQuickSearch(search)}
                >
                  {search}
                </Badge>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Recomendaciones para ti</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              {recommendations.map((rec) => (
                <div key={rec.id} className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{rec.title}</h4>
                      <p className="text-sm text-gray-600">{rec.description}</p>
                      <div className="flex gap-2 mt-2">
                        <Badge variant="secondary">{rec.grade}</Badge>
                        <Badge variant="secondary">{rec.subject}</Badge>
                        {rec.official && <Badge className="bg-green-100 text-green-800">Oficial SEP</Badge>}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              Resultados de búsqueda ({results.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              {results.map((result) => (
                <div key={result.id} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold">{result.title}</h3>
                      <p className="text-gray-600 mt-1">{result.description}</p>
                      
                      <div className="flex items-center gap-4 mt-3 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {result.author}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(result.date).toLocaleDateString('es-MX')}
                        </span>
                        <span className="flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          {result.rating}/5
                        </span>
                      </div>

                      <div className="flex gap-2 mt-3">
                        <Badge variant="outline">{result.type}</Badge>
                        <Badge variant="outline">{result.grade}</Badge>
                        <Badge variant="outline">{result.subject}</Badge>
                        {result.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary">{tag}</Badge>
                        ))}
                        {result.official && (
                          <Badge className="bg-green-100 text-green-800">Oficial SEP</Badge>
                        )}
                      </div>
                    </div>

                    <div className="flex gap-2 ml-4">
                      {result.previewUrl && (
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          Vista previa
                        </Button>
                      )}
                      <Button size="sm" onClick={() => handleDownload(result)}>
                        <Download className="h-4 w-4 mr-1" />
                        Descargar
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};