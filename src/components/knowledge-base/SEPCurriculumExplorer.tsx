import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Search, Filter, Download, ChevronRight, ChevronDown } from 'lucide-react';
import { sepCurriculumService } from '../../lib/sep-curriculum-api';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

interface SEPCurriculumExplorerProps {
  onSelectPDA?: (pda: any) => void;
  onSelectProject?: (project: any) => void;
  onSelectTemplate?: (template: any) => void;
}

export function SEPCurriculumExplorer({ 
  onSelectPDA, 
  onSelectProject, 
  onSelectTemplate 
}: SEPCurriculumExplorerProps) {
  const [curriculumStructure, setCurriculumStructure] = useState<any>(null);
  const [selectedGrade, setSelectedGrade] = useState<string>('1°');
  const [selectedSubject, setSelectedSubject] = useState<string>('');
  const [selectedField, setSelectedField] = useState<string>('');
  const [selectedAxis, setSelectedAxis] = useState<string>('');
  const [pdas, setPdas] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedFields, setExpandedFields] = useState<Set<string>>(new Set());

  const grades = ['1°', '2°', '3°', '4°', '5°', '6°'];
  const subjects = ['Español', 'Matemáticas', 'Exploración', 'Formación Cívica y Ética', 'Artes', 'Educación Física'];

  useEffect(() => {
    loadCurriculumData();
  }, [selectedGrade, selectedSubject]);

  const loadCurriculumData = async () => {
    try {
      setLoading(true);
      const [structure, pdasData, projectsData, templatesData] = await Promise.all([
        sepCurriculumService.getCurriculumStructure({ 
          grade: selectedGrade, 
          subject: selectedSubject || undefined 
        }),
        sepCurriculumService.getLearningPurposes({ 
          grade: selectedGrade, 
          subject: selectedSubject || undefined 
        }),
        sepCurriculumService.getProjects({ grade: selectedGrade }),
        sepCurriculumService.getOfficialTemplates({ grade: selectedGrade })
      ]);

      setCurriculumStructure(structure);
      setPdas(pdasData);
      setProjects(projectsData);
      setTemplates(templatesData);
    } catch (error) {
      console.error('Error loading curriculum data:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleFieldExpansion = (fieldId: string) => {
    setExpandedFields(prev => {
      const newSet = new Set(prev);
      if (newSet.has(fieldId)) {
        newSet.delete(fieldId);
      } else {
        newSet.add(fieldId);
      }
      return newSet;
    });
  };

  const handlePDASelect = (pda: any) => {
    onSelectPDA?.(pda);
  };

  const handleProjectSelect = (project: any) => {
    onSelectProject?.(project);
  };

  const handleTemplateSelect = (template: any) => {
    onSelectTemplate?.(template);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="w-5 h-5" />
            <span>Filtros de Búsqueda</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Grado</label>
              <Select value={selectedGrade} onValueChange={setSelectedGrade}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {grades.map(grade => (
                    <SelectItem key={grade} value={grade}>{grade}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Materia</label>
              <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                <SelectTrigger>
                  <SelectValue placeholder="Todas las materias" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todas las materias</SelectItem>
                  {subjects.map(subject => (
                    <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={loadCurriculumData} className="w-full">
                <Search className="w-4 h-4 mr-2" />
                Buscar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Curriculum Structure */}
      {curriculumStructure && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BookOpen className="w-5 h-5" />
              <span>Estructura Curricular - {curriculumStructure.framework.name}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {curriculumStructure.fields.map((field: any) => (
                <div key={field.id} className="border rounded-lg">
                  <div 
                    className="p-4 cursor-pointer hover:bg-gray-50 flex items-center justify-between"
                    onClick={() => toggleFieldExpansion(field.id)}
                  >
                    <div>
                      <h3 className="font-semibold">{field.name}</h3>
                      <p className="text-sm text-gray-600">{field.description}</p>
                    </div>
                    {expandedFields.has(field.id) ? 
                      <ChevronDown className="w-5 h-5" /> : 
                      <ChevronRight className="w-5 h-5" />
                    }
                  </div>
                  
                  {expandedFields.has(field.id) && (
                    <div className="px-4 pb-4">
                      <div className="space-y-3">
                        {field.axes.map((axis: any) => (
                          <div key={axis.id} className="ml-4 p-3 bg-gray-50 rounded">
                            <h4 className="font-medium">{axis.name} ({axis.code})</h4>
                            <p className="text-sm text-gray-600">{axis.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Learning Purposes (PDAs) */}
      <Card>
        <CardHeader>
          <CardTitle>Propósitos de Aprendizaje (PDAs)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {pdas.map((pda) => (
              <div key={pda.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge variant="outline">{pda.code}</Badge>
                      <Badge>{pda.grade}</Badge>
                      <Badge variant="secondary">{pda.subject}</Badge>
                    </div>
                    <h4 className="font-medium mb-2">{pda.purpose}</h4>
                    {pda.expectedLearning && (
                      <div className="text-sm text-gray-600">
                        <p className="font-medium mb-1">Aprendizajes esperados:</p>
                        <ul className="list-disc list-inside space-y-1">
                          {pda.expectedLearning.map((learning: string, index: number) => (
                            <li key={index}>{learning}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                  {onSelectPDA && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handlePDASelect(pda)}
                    >
                      Seleccionar
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Projects */}
      <Card>
        <CardHeader>
          <CardTitle>Proyectos Sociocríticos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {projects.map((project) => (
              <div key={project.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium mb-1">{project.name}</h4>
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge>{project.grade}</Badge>
                      <Badge variant="outline">{project.subject}</Badge>
                      <Badge variant="secondary">{project.type}</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{project.description}</p>
                    <div className="text-sm">
                      <p className="font-medium">Duración: {project.duration}</p>
                      <p className="font-medium mt-1">Objetivos:</p>
                      <ul className="list-disc list-inside">
                        {project.objectives.map((obj: string, index: number) => (
                          <li key={index}>{obj}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  {onSelectProject && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleProjectSelect(project)}
                    >
                      Ver detalles
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Templates */}
      <Card>
        <CardHeader>
          <CardTitle>Plantillas Oficiales</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {templates.map((template) => (
              <div key={template.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium mb-1">{template.name}</h4>
                    <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{template.type}</Badge>
                      {template.grade && <Badge>{template.grade}</Badge>}
                      {template.subject && <Badge variant="secondary">{template.subject}</Badge>}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleTemplateSelect(template)}
                    >
                      <Download className="w-4 h-4 mr-1" />
                      Usar
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}