import React, { useState, useEffect } from 'react';
import { Download, Star, Eye, Search } from 'lucide-react';
import { useToast } from '../../hooks/useToast';
import { SEPIntegration, SEPResource } from '../../lib/sep-integration';

interface TemplateLibraryProps {
  onTemplateSelect?: (template: SEPResource) => void;
  grade?: string;
  subject?: string;
}

export function TemplateLibrary({ onTemplateSelect, grade, subject }: TemplateLibraryProps) {
  const [templates, setTemplates] = useState<SEPResource[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<SEPResource[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [types, setTypes] = useState<string[]>([]);
  const { toast } = useToast();
  const sepIntegration = new SEPIntegration();

  useEffect(() => {
    loadTemplates();
    loadTypes();
  }, [grade, subject]);

  useEffect(() => {
    filterTemplates();
  }, [templates, searchTerm, selectedType]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const filters = {
        type: 'planeacion',
        grade,
        subject,
        limit: 100
      };
      
      const result = await sepIntegration.getResources(filters);
      setTemplates(result.resources);
    } catch {
      setTemplates([]);
      toast({
        title: 'Error',
        description: 'No se pudieron cargar las plantillas',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadTypes = async () => {
    try {
      const allTypes = await sepIntegration.getResourceTypes();
      setTypes(allTypes.filter((type: string) => type !== 'template'));
    } catch {
      setTypes([]);
    }
  };

  const filterTemplates = () => {
    let filtered = templates;

    if (searchTerm) {
      filtered = filtered.filter(template =>
        template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedType !== 'all') {
      filtered = filtered.filter(template => template.type === selectedType);
    }

    setFilteredTemplates(filtered);
  };

  const handleDownload = async (template: SEPResource) => {
    try {
      await sepIntegration.incrementDownload(template.id);
      toast({
        title: 'Plantilla descargada',
        description: `Has descargado: ${template.title}`,
      });
    } catch {
      toast({
        title: 'Error',
        description: 'No se pudo descargar la plantilla',
        variant: 'destructive',
      });
    }
  };

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'planeacion': 'bg-blue-100 text-blue-800',
      'actividad': 'bg-green-100 text-green-800',
      'proyecto': 'bg-purple-100 text-purple-800',
      'evaluacion': 'bg-orange-100 text-orange-800',
      'rubrica': 'bg-pink-100 text-pink-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Buscar plantillas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Todos los tipos</option>
              {types.map((type: string) => (
                <option key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="text-sm text-gray-600">
        Mostrando {filteredTemplates.length} de {templates.length} plantillas
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <div key={template.id} className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex justify-between items-start mb-3">
                <h3 className="text-lg font-semibold text-gray-900">{template.title}</h3>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(template.type)}`}>
                  {template.type}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 mb-4">{template.category}</p>
              
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <Download className="w-4 h-4" />
                    {template.downloads}
                  </span>
                  <span className="flex items-center gap-1">
                    <Star className="w-4 h-4" />
                    {template.rating}/5
                  </span>
                </div>
                {template.grade && (
                  <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                    {template.grade}
                  </span>
                )}
              </div>

              <div className="flex gap-2">
                <button
                  onClick={() => {
                    if (onTemplateSelect) {
                      onTemplateSelect(template);
                    }
                  }}
                  className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  <Eye className="w-4 h-4" />
                  Vista previa
                </button>
                <button
                  onClick={() => handleDownload(template)}
                  className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  Descargar
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron plantillas</h3>
          <p className="text-gray-600">
            Intenta ajustar tus filtros de búsqueda
          </p>
        </div>
      )}
    </div>
  );
}