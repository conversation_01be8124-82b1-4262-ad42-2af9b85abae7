import React, { useState, useEffect } from 'react';
import { usePlanningWizardStore } from '../../stores/planningWizardStore';
import { validationEngine, ValidationResult } from '../../lib/validation-engine';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { CheckCircle2, AlertCircle, XCircle, Lightbulb, TrendingUp } from 'lucide-react';

interface ValidationPanelProps {
  onValidationComplete?: (result: ValidationResult) => void;
}

export const ValidationPanel: React.FC<ValidationPanelProps> = ({ onValidationComplete }) => {
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const wizardData = usePlanningWizardStore(state => state.wizardData);

  useEffect(() => {
    const validatePlanning = async () => {
      if (!wizardData || !wizardData.step1) return;

      setIsValidating(true);
      try {
        // Ensure we have a complete WizardData object
        const completeData = {
          step1: wizardData.step1 || { title: '', subject: '', grade: '', school: '', startDate: '', endDate: '', duration: 50 },
          step2: wizardData.step2 || { formativeField: '', articulatingAxes: [] },
          step3: wizardData.step3 || { pda: '', description: '', objectives: [] },
          step4: wizardData.step4 || { activities: [] },
          step5: wizardData.step5 || { evaluation: { instruments: [], criteria: [], evidences: [] } },
          step6: wizardData.step6 || { resources: [], observations: '' }
        };
        
        const result = await validationEngine.validatePlanning(completeData);
        setValidationResult(result);
        onValidationComplete?.(result);
      } catch (error) {
        console.error('Error validating planning:', error);
      } finally {
        setIsValidating(false);
      }
    };

    validatePlanning();
  }, [wizardData, onValidationComplete]);

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'fair': return 'text-yellow-600 bg-yellow-100';
      case 'needs-improvement': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'excellent': return <CheckCircle2 className="h-5 w-5" />;
      case 'good': return <TrendingUp className="h-5 w-5" />;
      case 'fair': return <AlertCircle className="h-5 w-5" />;
      case 'needs-improvement': return <XCircle className="h-5 w-5" />;
      default: return <Lightbulb className="h-5 w-5" />;
    }
  };

  const getLevelText = (level: string) => {
    switch (level) {
      case 'excellent': return 'Excelente';
      case 'good': return 'Bueno';
      case 'fair': return 'Regular';
      case 'needs-improvement': return 'Necesita Mejora';
      default: return 'Desconocido';
    }
  };

  if (isValidating) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3">Validando planeación...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!validationResult) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8 text-gray-500">
            No hay datos para validar
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getLevelIcon(validationResult.level)}
            Validación de Planeación
          </CardTitle>
          <CardDescription>
            Análisis integral de calidad pedagógica
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-lg font-semibold">
                Puntuación General: {validationResult.percentage}%
              </span>
              <Badge className={getLevelColor(validationResult.level)}>
                {getLevelText(validationResult.level)}
              </Badge>
            </div>
            <Progress value={validationResult.percentage} className="h-2" />
            <div className="text-sm text-gray-600">
              {validationResult.score} / {validationResult.maxScore} puntos
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Validation Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Categorías de Validación</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {validationResult.categories.map((category, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{category.name}</h4>
                  <span className="text-sm font-semibold">{category.percentage}%</span>
                </div>
                <Progress value={category.percentage} className="h-2 mb-2" />
                <ul className="text-sm space-y-1">
                  {category.details.map((detail, detailIndex) => (
                    <li key={detailIndex} className={`${detail.startsWith('✓') ? 'text-green-600' : 'text-orange-600'}`}>
                      {detail}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      {validationResult.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Recomendaciones de Mejora
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {validationResult.recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start gap-2">
                  <Lightbulb className="h-4 w-4 mt-0.5 text-blue-500 flex-shrink-0" />
                  <span className="text-sm">{recommendation}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Warnings */}
      {validationResult.warnings.length > 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-4 w-4 mt-0.5 text-yellow-600 flex-shrink-0" />
              <div>
                <h5 className="font-medium text-yellow-800 mb-2">Advertencias</h5>
                <ul className="space-y-1 text-sm text-yellow-700">
                  {validationResult.warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Errors */}
      {validationResult.errors.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-4">
            <div className="flex items-start gap-2">
              <XCircle className="h-4 w-4 mt-0.5 text-red-600 flex-shrink-0" />
              <div>
                <h5 className="font-medium text-red-800 mb-2">Errores Críticos</h5>
                <ul className="space-y-1 text-sm text-red-700">
                  {validationResult.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};