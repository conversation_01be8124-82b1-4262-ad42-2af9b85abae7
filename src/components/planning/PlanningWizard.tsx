import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ChevronLeft, ChevronRight, Save } from 'lucide-react';
import { usePlanningWizardStore } from '@/stores/planningWizardStore';
import { Step1GeneralInfo } from './steps/Step1GeneralInfo';
import { Step2FieldsAndAxes } from './steps/Step2FieldsAndAxes';
import { Step3LearningProcess } from './steps/Step3LearningProcess';
import { Step4Activities } from './steps/Step4Activities';
import { Step5Evaluation } from './steps/Step5Evaluation';
import { Step6ReviewAndExport } from './steps/Step6ReviewAndExport';

const STEP_TITLES = [
  'Información General',
  'Campo Formativo y Ejes',
  'Proceso de Desarrollo',
  'Actividades y Metodología',
  'Evaluación',
  'Revisión y Exportación',
];

const STEP_COMPONENTS = [
  Step1GeneralInfo,
  Step2FieldsAndAxes,
  Step3LearningProcess,
  Step4Activities,
  Step5Evaluation,
  Step6ReviewAndExport,
];

export function PlanningWizard() {
  const {
    currentStep,
    setCurrentStep,
    isValid,
    canProceedToStep,
    wizardData,
  } = usePlanningWizardStore();

  const totalSteps = STEP_TITLES.length;
  const progress = (currentStep / totalSteps) * 100;
  const CurrentStepComponent = STEP_COMPONENTS[currentStep - 1];

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleNext = () => {
    if (currentStep < totalSteps && canProceedToStep(currentStep + 1)) {
      setCurrentStep(currentStep + 1);
    }
  };

  const canGoNext = currentStep < totalSteps && isValid[currentStep];
  const canGoPrevious = currentStep > 1;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Progress Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">
                Paso {currentStep} de {totalSteps}: {STEP_TITLES[currentStep - 1]}
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Completa la información requerida para continuar
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium text-gray-600">
                {Math.round(progress)}% completado
              </div>
            </div>
          </div>
          <Progress value={progress} className="mt-4" />
        </CardHeader>
      </Card>

      {/* Step Content */}
      <Card>
        <CardContent className="p-6">
          <CurrentStepComponent />
        </CardContent>
      </Card>

      {/* Navigation */}
      <Card>
        <CardContent className="p-4">
          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={!canGoPrevious}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="w-4 h-4" />
              <span>Anterior</span>
            </Button>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="flex items-center space-x-2"
              >
                <Save className="w-4 h-4" />
                <span>Guardar Borrador</span>
              </Button>

              {currentStep < totalSteps ? (
                <Button
                  onClick={handleNext}
                  disabled={!canGoNext}
                  className="flex items-center space-x-2"
                >
                  <span>Siguiente</span>
                  <ChevronRight className="w-4 h-4" />
                </Button>
              ) : (
                <Button
                  disabled={!isValid[currentStep]}
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
                >
                  <span>Finalizar Planeación</span>
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}