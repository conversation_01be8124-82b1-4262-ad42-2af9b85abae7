import React, { useState } from 'react';
import { Download } from 'lucide-react';
import { useToast } from '../../hooks/useToast';
import { exportUtils, PlanningData } from '../../lib/export';

interface ExportPanelProps {
  planningData: PlanningData;
  onClose?: () => void;
}

export function ExportPanel({ planningData, onClose }: ExportPanelProps) {
  const [format, setFormat] = useState<'pdf' | 'word' | 'json' | 'html' | 'text'>('pdf');
  const [template, setTemplate] = useState<'planeacion-nem' | 'actividad-socioconstructivista' | 'proyecto-comunitario'>('planeacion-nem');
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();

  const handleExport = async () => {
    if (!planningData.title) {
      toast({
        title: 'Error',
        description: 'Por favor, completa al menos el título de la planeación antes de exportar',
        variant: 'destructive',
      });
      return;
    }

    setIsExporting(true);
    
    try {
      const filename = `planeacion-${planningData.title?.toLowerCase().replace(/\s+/g, '-') || 'nem'}`;
      
      switch (format) {
        case 'pdf':
          await exportUtils.exportToPDF(planningData, filename);
          break;
        case 'word':
          exportUtils.exportToWord(planningData, filename);
          break;
        case 'json':
          exportUtils.exportToJSON(planningData, filename);
          break;
        case 'html':
        case 'text': {
          const exporter = new exportUtils.DocumentExporter();
          const content = await exporter.exportDocument(planningData, {
            format,
            template,
            includeSections: ['all'],
            language: 'es',
            includeMetadata
          });
          
          if (typeof content === 'string') {
            exporter.downloadFile(content, filename, format);
          }
          break;
        }
      }

      toast({
        title: 'Exportación exitosa',
        description: `Tu planeación ha sido exportada en formato ${format.toUpperCase()}`,
      });
      
      if (onClose) onClose();
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Error al exportar',
        description: 'Hubo un problema al generar el documento. Por favor, intenta nuevamente.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const formatOptions = [
    { value: 'pdf' as const, label: 'PDF', description: 'Documento oficial con formato SEP' },
    { value: 'word' as const, label: 'Word/HTML', description: 'Editable en procesadores de texto' },
    { value: 'json' as const, label: 'JSON', description: 'Formato técnico para intercambio de datos' },
    { value: 'text' as const, label: 'Texto Plano', description: 'Formato simple y universal' },
  ];

  const templateOptions = [
    { value: 'planeacion-nem' as const, label: 'Planeación NEM Oficial', description: 'Formato oficial SEP para planeaciones completas' },
    { value: 'actividad-socioconstructivista' as const, label: 'Actividad Socioconstructivista', description: 'Plantilla para actividades individuales' },
    { value: 'proyecto-comunitario' as const, label: 'Proyecto Comunitario', description: 'Estructura para proyectos de aula y comunidad' },
  ];

  return (
    <div className="w-full">
      <div className="bg-white border rounded-lg p-6 space-y-6">
        <h3 className="text-lg font-semibold flex items-center space-x-2">
          <Download className="w-5 h-5" />
          <span>Exportar Planeación</span>
        </h3>

        {/* Format Selection */}
        <div className="space-y-4">
          <label className="block text-sm font-medium">Formato de Exportación</label>
          <div className="grid grid-cols-2 gap-3">
            {formatOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => setFormat(option.value)}
                className={`p-4 border rounded-lg text-left transition-all ${
                  format === option.value
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div>
                  <div className="font-medium">{option.label}</div>
                  <div className="text-sm text-gray-600">{option.description}</div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Template Selection */}
        <div className="space-y-2">
          <label className="block text-sm font-medium">Plantilla de Documento</label>
          <select
            value={template}
            onChange={(e) => setTemplate(e.target.value as typeof template)}
            className="w-full p-2 border rounded-md"
          >
            {templateOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label} - {option.description}
              </option>
            ))}
          </select>
        </div>

        {/* Options */}
        <div className="space-y-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={includeMetadata}
              onChange={(e) => setIncludeMetadata(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Incluir metadatos</span>
          </label>
        </div>

        {/* Preview */}
        <div className="border rounded-lg p-4 bg-gray-50">
          <h4 className="font-medium mb-2">Vista previa de configuración</h4>
          <div className="text-sm space-y-1">
            <p><strong>Formato:</strong> {format.toUpperCase()}</p>
            <p><strong>Plantilla:</strong> {exportUtils.OFFICIAL_TEMPLATES[template].name}</p>
            <p><strong>Metadatos:</strong> {includeMetadata ? 'Incluidos' : 'No incluidos'}</p>
            <p><strong>Archivo:</strong> planeacion-{planningData.title?.toLowerCase().replace(/\s+/g, '-') || 'nem'}.{format}</p>
          </div>
        </div>

        {/* Export Button */}
        <button
          onClick={handleExport}
          disabled={isExporting}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center space-x-2"
        >
          {isExporting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Generando documento...</span>
            </>
          ) : (
            <>
              <Download className="w-4 h-4" />
              <span>Exportar en {format.toUpperCase()}</span>
            </>
          )}
        </button>

        {/* Tips */}
        <div className="text-sm text-gray-600 space-y-1">
          <p><strong>Consejo:</strong> El formato PDF es ideal para compartir con supervisores y cumplir con requisitos oficiales.</p>
          <p>El formato Word/HTML permite ediciones posteriores.</p>
          <p>JSON es útil para respaldos y transferencia entre plataformas.</p>
        </div>
      </div>
    </div>
  );
}