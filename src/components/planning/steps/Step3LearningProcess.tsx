import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Trash2, Wand2 } from 'lucide-react';
import { usePlanningWizardStore, PlanningStep3 } from '@/stores/planningWizardStore';
import { aiApi } from '@/lib/api';
import { useToast } from '@/hooks/useToast';

const step3Schema = z.object({
  pda: z.string().min(1, 'Debes seleccionar un PDA'),
  description: z.string().min(10, 'La descripción debe tener al menos 10 caracteres'),
  objectives: z.array(z.string().min(1)).min(1, 'Debes tener al menos un objetivo'),
});

// Sample PDAs based on formative fields
const SAMPLE_PDAS = {
  'lenguajes': [
    'Participa en intercambios orales mediante la formulación de preguntas.',
    'Lee textos narrativos, informativos y publicitarios.',
    'Escribe textos narrativos, informativos y publicitarios.',
  ],
  'saberes-cientifico': [
    'Propone explicaciones a partir de observaciones y experimentos.',
    'Resuelve problemas mediante el uso de estrategias matemáticas.',
    'Diseña y construye prototipos para resolver problemas.',
  ],
  'etica-naturaleza': [
    'Comprende procesos de cambio en el tiempo histórico.',
    'Reconoce la diversidad natural y cultural del territorio.',
    'Participa en acciones para el cuidado del ambiente.',
  ],
  'humano-comunitario': [
    'Desarrolla su creatividad e imaginación artística.',
    'Fortalece su autoestima y autorregulación emocional.',
    'Participa en actividades físicas y deportivas.',
  ],
};

export function Step3LearningProcess() {
  const { wizardData, updateStepData, setStepValid } = usePlanningWizardStore();
  const { toast } = useToast();
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [customObjective, setCustomObjective] = useState('');

  const {
    register,
    watch,
    setValue,
    getValues,
    formState: { errors, isValid },
  } = useForm<PlanningStep3>({
    resolver: zodResolver(step3Schema),
    defaultValues: wizardData.step3,
    mode: 'onChange',
  });

  const watchedData = watch();
  const currentObjectives = watch('objectives') || [];
  const formativeField = wizardData.step2?.formativeField;

  // Update store when form data changes
  useEffect(() => {
    const timer = setTimeout(() => {
      updateStepData('step3', watchedData);
      setStepValid(3, isValid);
    }, 100);

    return () => clearTimeout(timer);
  }, [watchedData, isValid, updateStepData, setStepValid]);

  const addObjective = () => {
    if (customObjective.trim()) {
      const newObjectives = [...currentObjectives, customObjective.trim()];
      setValue('objectives', newObjectives);
      setCustomObjective('');
    }
  };

  const removeObjective = (index: number) => {
    const newObjectives = currentObjectives.filter((_, i) => i !== index);
    setValue('objectives', newObjectives);
  };

  const selectPDA = (pda: string) => {
    setValue('pda', pda);
  };

  const generateContent = async () => {
    setIsGenerating(true);
    try {
      const context = {
        formativeField: wizardData.step2?.formativeField || '',
        grade: wizardData.step1?.grade || '',
        subject: wizardData.step1?.subject || '',
        articulatingAxes: wizardData.step2?.articulatingAxes || [],
      };

      const result = await aiApi.generateContent('objectives', context);
      
      if (result.data?.content) {
        const content = result.data.content;
        
        // If content is structured, extract objectives
        if (Array.isArray(content.objectives)) {
          setValue('objectives', content.objectives);
        } else if (typeof content === 'string') {
          // Parse text content for objectives
          const objectives = content.split('\n')
            .filter(line => line.trim().startsWith('-') || line.trim().startsWith('•'))
            .map(line => line.replace(/^[-•]\s*/, '').trim())
            .filter(obj => obj.length > 0);
          
          if (objectives.length > 0) {
            setValue('objectives', objectives);
          }
        }

        toast({
          title: 'Contenido generado',
          description: 'Se han sugerido objetivos basados en tu planeación',
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No se pudo generar el contenido. Intenta nuevamente.',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const availablePDAs = formativeField ? SAMPLE_PDAS[formativeField as keyof typeof SAMPLE_PDAS] || [] : [];

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">
          Proceso de Desarrollo de Aprendizaje (PDA)
        </h2>
        <p className="text-gray-600">
          Define el proceso de aprendizaje y los objetivos específicos
        </p>
      </div>

      <form className="space-y-6">
        {/* PDA Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Proceso de Desarrollo de Aprendizaje</CardTitle>
            <p className="text-sm text-gray-600">
              Selecciona el PDA principal que guiará tu planeación
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {availablePDAs.length > 0 ? (
              <div className="space-y-3">
                {availablePDAs.map((pda, index) => (
                  <div
                    key={index}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      watch('pda') === pda
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => selectPDA(pda)}
                  >
                    <div className="flex items-start space-x-3">
                      <input
                        type="radio"
                        value={pda}
                        checked={watch('pda') === pda}
                        onChange={() => selectPDA(pda)}
                        className="mt-1"
                      />
                      <p className="text-sm text-gray-800">{pda}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Selecciona un Campo Formativo en el paso anterior para ver los PDAs disponibles</p>
              </div>
            )}
            
            {errors.pda && (
              <p className="text-sm text-red-600">{errors.pda.message}</p>
            )}
          </CardContent>
        </Card>

        {/* Description */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Descripción del Proceso</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="description">
                Describe cómo se desarrollará el proceso de aprendizaje *
              </Label>
              <Textarea
                id="description"
                placeholder="Explica cómo los estudiantes desarrollarán las competencias del PDA seleccionado, qué estrategias se utilizarán y cómo se integrará con los ejes articuladores..."
                rows={4}
                {...register('description')}
              />
              {errors.description && (
                <p className="text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Learning Objectives */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Objetivos de Aprendizaje</CardTitle>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={generateContent}
                disabled={isGenerating}
                className="flex items-center space-x-2"
              >
                <Wand2 className="w-4 h-4" />
                <span>{isGenerating ? 'Generando...' : 'Generar con IA'}</span>
              </Button>
            </div>
            <p className="text-sm text-gray-600">
              Define los objetivos específicos que los estudiantes alcanzarán
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Current Objectives */}
            {currentObjectives.length > 0 && (
              <div className="space-y-2">
                {currentObjectives.map((objective, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-2 p-3 bg-gray-50 rounded-lg"
                  >
                    <span className="text-sm font-medium text-gray-500 mt-0.5">
                      {index + 1}.
                    </span>
                    <p className="flex-1 text-sm text-gray-800">{objective}</p>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeObjective(index)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {/* Add New Objective */}
            <div className="flex space-x-2">
              <Input
                placeholder="Escribe un nuevo objetivo de aprendizaje..."
                value={customObjective}
                onChange={(e) => setCustomObjective(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addObjective();
                  }
                }}
              />
              <Button
                type="button"
                onClick={addObjective}
                disabled={!customObjective.trim()}
                className="flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Agregar</span>
              </Button>
            </div>

            {errors.objectives && (
              <p className="text-sm text-red-600">{errors.objectives.message}</p>
            )}
          </CardContent>
        </Card>

        {/* Help Card */}
        <Card className="bg-purple-50 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <div className="w-5 h-5 rounded-full bg-purple-600 flex items-center justify-center mt-0.5">
                <span className="text-white text-xs font-bold">📝</span>
              </div>
              <div>
                <h4 className="font-medium text-purple-900">Consejos para objetivos efectivos</h4>
                <ul className="text-sm text-purple-800 mt-2 space-y-1">
                  <li>• Usa verbos de acción específicos (analizar, crear, evaluar, etc.)</li>
                  <li>• Asegúrate de que sean medibles y observables</li>
                  <li>• Conecta con los ejes articuladores seleccionados</li>
                  <li>• Considera las características de tus estudiantes</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}