import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, Trash2, Wand2, CheckCircle } from 'lucide-react';
import { usePlanningWizardStore, PlanningStep5 } from '@/stores/planningWizardStore';
import { aiApi } from '@/lib/api';
import { useToast } from '@/hooks/useToast';

const step5Schema = z.object({
  evaluation: z.object({
    instruments: z.array(z.string()).min(1, 'Debes seleccionar al menos un instrumento'),
    criteria: z.array(z.string()).min(1, 'Debes definir al menos un criterio'),
    evidences: z.array(z.string()).min(1, 'Debes especificar al menos una evidencia'),
  }),
});

const EVALUATION_INSTRUMENTS = [
  {
    id: 'rubrica',
    name: 'Rúbrica Analítica',
    description: 'Evaluación detallada con criterios específicos y niveles de desempeño',
    nemAlignment: 'Evalúa competencias integrales y proceso de aprendizaje',
  },
  {
    id: 'lista-cotejo',
    name: 'Lista de Cotejo',
    description: 'Verificación de presencia/ausencia de elementos específicos',
    nemAlignment: 'Seguimiento puntual de indicadores de logro',
  },
  {
    id: 'portafolio',
    name: 'Portafolio de Evidencias',
    description: 'Colección sistemática de trabajos del estudiante',
    nemAlignment: 'Muestra la evolución y reflexión del aprendizaje',
  },
  {
    id: 'autoevaluacion',
    name: 'Autoevaluación',
    description: 'Reflexión del estudiante sobre su propio aprendizaje',
    nemAlignment: 'Fomenta metacognición y autorregulación',
  },
  {
    id: 'coevaluacion',
    name: 'Coevaluación',
    description: 'Evaluación entre pares',
    nemAlignment: 'Desarrolla pensamiento crítico y colaboración',
  },
  {
    id: 'proyecto',
    name: 'Evaluación por Proyecto',
    description: 'Valoración integral de proyectos comunitarios/escolares',
    nemAlignment: 'Integra aprendizajes y vinculación comunitaria',
  },
];

const SAMPLE_CRITERIA = {
  'lenguajes': [
    'Comprensión lectora contextualizada',
    'Expresión oral clara y respetuosa',
    'Producción escrita coherente',
    'Uso apropiado del lenguaje formal e informal',
    'Integración de elementos culturales en la comunicación',
  ],
  'saberes-cientifico': [
    'Aplicación del método científico',
    'Formulación de hipótesis fundamentadas',
    'Análisis de datos e información',
    'Comunicación de resultados científicos',
    'Pensamiento crítico ante fenómenos naturales',
  ],
  'etica-naturaleza': [
    'Comprensión de procesos históricos',
    'Análisis crítico de fuentes',
    'Participación ciudadana responsable',
    'Conciencia ambiental y sustentabilidad',
    'Respeto a la diversidad cultural',
  ],
  'humano-comunitario': [
    'Expresión artística y creatividad',
    'Autorregulación emocional',
    'Colaboración y trabajo en equipo',
    'Actividad física y vida saludable',
    'Participación comunitaria activa',
  ],
};

export function Step5Evaluation() {
  const { wizardData, updateStepData, setStepValid } = usePlanningWizardStore();
  const { toast } = useToast();
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [newCriterion, setNewCriterion] = useState('');
  const [newEvidence, setNewEvidence] = useState('');

  const {
    register,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<PlanningStep5>({
    resolver: zodResolver(step5Schema),
    defaultValues: wizardData.step5,
    mode: 'onChange',
  });

  const watchedData = watch();
  const selectedInstruments = watch('evaluation.instruments') || [];
  const criteria = watch('evaluation.criteria') || [];
  const evidences = watch('evaluation.evidences') || [];

  // Update store when form data changes
  useEffect(() => {
    const timer = setTimeout(() => {
      updateStepData('step5', watchedData);
      setStepValid(5, isValid);
    }, 100);

    return () => clearTimeout(timer);
  }, [watchedData, isValid, updateStepData, setStepValid]);

  const formativeField = wizardData.step2?.formativeField;
  const activities = wizardData.step4?.activities || [];
  const objectives = wizardData.step3?.objectives || [];

  const handleInstrumentChange = (instrumentId: string, checked: boolean) => {
    const current = selectedInstruments || [];
    if (checked) {
      setValue('evaluation.instruments', [...current, instrumentId]);
    } else {
      setValue('evaluation.instruments', current.filter(id => id !== instrumentId));
    }
  };

  const addCriterion = () => {
    if (newCriterion.trim()) {
      setValue('evaluation.criteria', [...criteria, newCriterion.trim()]);
      setNewCriterion('');
    }
  };

  const removeCriterion = (index: number) => {
    setValue('evaluation.criteria', criteria.filter((_, i) => i !== index));
  };

  const addEvidence = () => {
    if (newEvidence.trim()) {
      setValue('evaluation.evidences', [...evidences, newEvidence.trim()]);
      setNewEvidence('');
    }
  };

  const removeEvidence = (index: number) => {
    setValue('evaluation.evidences', evidences.filter((_, i) => i !== index));
  };

  const generateEvaluationContent = async () => {
    setIsGenerating(true);
    try {
      const context = {
        formativeField: wizardData.step2?.formativeField || '',
        grade: wizardData.step1?.grade || '',
        subject: wizardData.step1?.subject || '',
        objectives,
        activities: activities.map(act => ({
          name: act.name,
          type: act.type,
          description: act.description,
        })),
      };

      const result = await aiApi.generateContent('evaluation', context);
      
      if (result.data?.content) {
        const content = result.data.content;
        
        // Parse generated content
        if (content.criteria && Array.isArray(content.criteria)) {
          setValue('evaluation.criteria', content.criteria);
        }
        
        if (content.evidences && Array.isArray(content.evidences)) {
          setValue('evaluation.evidences', content.evidences);
        }

        // Suggest appropriate instruments based on content
        if (content.recommendedInstruments && Array.isArray(content.recommendedInstruments)) {
          setValue('evaluation.instruments', content.recommendedInstruments);
        }

        toast({
          title: 'Evaluación generada',
          description: 'Se han creado criterios y evidencias basados en tus actividades y objetivos',
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No se pudo generar el contenido de evaluación. Intenta nuevamente.',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const loadSampleCriteria = () => {
    if (formativeField && SAMPLE_CRITERIA[formativeField as keyof typeof SAMPLE_CRITERIA]) {
      const samples = SAMPLE_CRITERIA[formativeField as keyof typeof SAMPLE_CRITERIA];
      setValue('evaluation.criteria', samples);
      toast({
        title: 'Criterios cargados',
        description: `Se han cargado criterios sugeridos para ${formativeField}`,
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">
          Evaluación Formativa NEM
        </h2>
        <p className="text-gray-600">
          Diseña instrumentos de evaluación alineados con la Nueva Escuela Mexicana
        </p>
      </div>

      <form className="space-y-6">
        {/* Evaluation Instruments */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Instrumentos de Evaluación</CardTitle>
            <p className="text-sm text-gray-600">
              Selecciona los instrumentos que utilizarás para evaluar el aprendizaje
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {EVALUATION_INSTRUMENTS.map((instrument) => (
                <div
                  key={instrument.id}
                  className={`p-4 border rounded-lg transition-all ${
                    selectedInstruments.includes(instrument.id)
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id={instrument.id}
                      checked={selectedInstruments.includes(instrument.id)}
                      onCheckedChange={(checked) => 
                        handleInstrumentChange(instrument.id, checked as boolean)
                      }
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <Label 
                        htmlFor={instrument.id} 
                        className="font-semibold text-gray-900 cursor-pointer"
                      >
                        {instrument.name}
                      </Label>
                      <p className="text-sm text-gray-600 mt-1">{instrument.description}</p>
                      <p className="text-xs text-green-700 mt-2 bg-green-100 px-2 py-1 rounded">
                        <CheckCircle className="w-3 h-3 inline mr-1" />
                        {instrument.nemAlignment}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {errors.evaluation?.instruments && (
              <p className="text-sm text-red-600">{errors.evaluation.instruments.message}</p>
            )}
          </CardContent>
        </Card>

        {/* Evaluation Criteria */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Criterios de Evaluación</CardTitle>
                <p className="text-sm text-gray-600">
                  Define qué aspectos específicos evaluarás del aprendizaje
                </p>
              </div>
              <div className="flex space-x-2">
                {formativeField && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={loadSampleCriteria}
                  >
                    Cargar Sugerencias
                  </Button>
                )}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateEvaluationContent}
                  disabled={isGenerating}
                  className="flex items-center space-x-2"
                >
                  <Wand2 className="w-4 h-4" />
                  <span>{isGenerating ? 'Generando...' : 'Generar con IA'}</span>
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {criteria.length > 0 && (
              <div className="space-y-2">
                {criteria.map((criterion, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-2 p-3 bg-gray-50 rounded-lg"
                  >
                    <span className="text-sm font-medium text-gray-500 mt-0.5">
                      {index + 1}.
                    </span>
                    <p className="flex-1 text-sm text-gray-800">{criterion}</p>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeCriterion(index)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            <div className="flex space-x-2">
              <Input
                placeholder="Nuevo criterio de evaluación..."
                value={newCriterion}
                onChange={(e) => setNewCriterion(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addCriterion();
                  }
                }}
              />
              <Button
                type="button"
                onClick={addCriterion}
                disabled={!newCriterion.trim()}
                className="flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Agregar</span>
              </Button>
            </div>

            {errors.evaluation?.criteria && (
              <p className="text-sm text-red-600">{errors.evaluation.criteria.message}</p>
            )}
          </CardContent>
        </Card>

        {/* Evidence of Learning */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Evidencias de Aprendizaje</CardTitle>
            <p className="text-sm text-gray-600">
              Especifica qué productos o desempeños demostrarán el logro de los objetivos
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {evidences.length > 0 && (
              <div className="space-y-2">
                {evidences.map((evidence, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-2 p-3 bg-blue-50 rounded-lg"
                  >
                    <span className="text-sm font-medium text-blue-600 mt-0.5">
                      {index + 1}.
                    </span>
                    <p className="flex-1 text-sm text-gray-800">{evidence}</p>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeEvidence(index)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            <div className="flex space-x-2">
              <Input
                placeholder="Nueva evidencia de aprendizaje..."
                value={newEvidence}
                onChange={(e) => setNewEvidence(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addEvidence();
                  }
                }}
              />
              <Button
                type="button"
                onClick={addEvidence}
                disabled={!newEvidence.trim()}
                className="flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Agregar</span>
              </Button>
            </div>

            {errors.evaluation?.evidences && (
              <p className="text-sm text-red-600">{errors.evaluation.evidences.message}</p>
            )}
          </CardContent>
        </Card>

        {/* Activity-Evidence Alignment */}
        {activities.length > 0 && (
          <Card className="bg-indigo-50 border-indigo-200">
            <CardHeader>
              <CardTitle className="text-lg text-indigo-900">
                Alineación Actividades-Evaluación
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {activities.map((activity, index) => (
                  <div key={activity.id} className="p-3 bg-white rounded border">
                    <h4 className="font-medium text-indigo-900">{activity.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      Tipo: {activity.type} • Duración: {activity.duration} min
                    </p>
                    <p className="text-xs text-indigo-700 mt-2">
                      💡 Considera qué evidencias específicas puede generar esta actividad
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Help Card */}
        <Card className="bg-emerald-50 border-emerald-200">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <div className="w-5 h-5 rounded-full bg-emerald-600 flex items-center justify-center mt-0.5">
                <span className="text-white text-xs font-bold">📊</span>
              </div>
              <div>
                <h4 className="font-medium text-emerald-900">Principios de Evaluación NEM</h4>
                <ul className="text-sm text-emerald-800 mt-2 space-y-1">
                  <li>• Evalúa el proceso, no solo el resultado final</li>
                  <li>• Incorpora autoevaluación y coevaluación para desarrollar autonomía</li>
                  <li>• Considera la diversidad de formas de demostrar el aprendizaje</li>
                  <li>• Conecta la evaluación con los ejes articuladores seleccionados</li>
                  <li>• Proporciona retroalimentación formativa continua</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}