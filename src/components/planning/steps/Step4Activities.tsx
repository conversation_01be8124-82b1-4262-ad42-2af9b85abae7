import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Trash2, Edit2, Wand2 } from 'lucide-react';
import { usePlanningWizardStore } from '@/stores/planningWizardStore';
import { useEffect } from 'react';

interface Activity {
  id: string;
  name: string;
  description: string;
  duration: number;
  type: string;
  materials: string[];
}

const ACTIVITY_TYPES = [
  'Exploración de conocimientos previos',
  'Desarrollo conceptual',
  'Práctica guiada',
  'Práctica independiente',
  'Aplicación',
  'Evaluación formativa',
  'Cierre y síntesis',
  'Proyecto comunitario',
];

export function Step4Activities() {
  const { wizardData, updateStepData, setStepValid } = usePlanningWizardStore();
  const [editingActivity, setEditingActivity] = useState<Activity | null>(null);
  const [showForm, setShowForm] = useState(false);

  const activities = wizardData.step4?.activities || [];

  const [formData, setFormData] = useState<Partial<Activity>>({
    name: '',
    description: '',
    duration: 15,
    type: '',
    materials: [],
  });

  const [materialInput, setMaterialInput] = useState('');

  useEffect(() => {
    // Validate step - must have at least one activity
    const timer = setTimeout(() => {
      const isValid = activities.length > 0;
      setStepValid(4, isValid);
    }, 100);

    return () => clearTimeout(timer);
  }, [activities.length, setStepValid]);

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      duration: 15,
      type: '',
      materials: [],
    });
    setMaterialInput('');
    setEditingActivity(null);
    setShowForm(false);
  };

  const handleSaveActivity = () => {
    if (!formData.name || !formData.description || !formData.type) return;

    const activityData: Activity = {
      id: editingActivity?.id || `activity-${Date.now()}`,
      name: formData.name!,
      description: formData.description!,
      duration: formData.duration || 15,
      type: formData.type!,
      materials: formData.materials || [],
    };

    let newActivities;
    if (editingActivity) {
      newActivities = activities.map(act => 
        act.id === editingActivity.id ? activityData : act
      );
    } else {
      newActivities = [...activities, activityData];
    }

    updateStepData('step4', { activities: newActivities });
    resetForm();
  };

  const handleEditActivity = (activity: Activity) => {
    setFormData(activity);
    setEditingActivity(activity);
    setShowForm(true);
  };

  const handleDeleteActivity = (id: string) => {
    const newActivities = activities.filter(act => act.id !== id);
    updateStepData('step4', { activities: newActivities });
  };

  const addMaterial = () => {
    if (materialInput.trim()) {
      const newMaterials = [...(formData.materials || []), materialInput.trim()];
      setFormData(prev => ({ ...prev, materials: newMaterials }));
      setMaterialInput('');
    }
  };

  const removeMaterial = (index: number) => {
    const newMaterials = (formData.materials || []).filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, materials: newMaterials }));
  };

  const totalDuration = activities.reduce((sum, act) => sum + act.duration, 0);

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">
          Actividades y Metodología
        </h2>
        <p className="text-gray-600">
          Diseña las actividades que desarrollarán los aprendizajes esperados
        </p>
      </div>

      {/* Activities Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Actividades Planificadas</CardTitle>
              <p className="text-sm text-gray-600">
                {activities.length} actividades • {totalDuration} minutos total
              </p>
            </div>
            <Button
              onClick={() => setShowForm(true)}
              className="flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Nueva Actividad</span>
            </Button>
          </div>
        </CardHeader>
        
        {activities.length > 0 && (
          <CardContent>
            <div className="space-y-4">
              {activities.map((activity, index) => (
                <div
                  key={activity.id}
                  className="p-4 border rounded-lg hover:border-gray-300 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-sm font-medium text-gray-500">
                          {index + 1}.
                        </span>
                        <h3 className="font-semibold text-gray-900">{activity.name}</h3>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          {activity.type}
                        </span>
                        <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                          {activity.duration} min
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{activity.description}</p>
                      {activity.materials.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          <span className="text-xs text-gray-500">Materiales:</span>
                          {activity.materials.map((material, i) => (
                            <span
                              key={i}
                              className="text-xs bg-gray-50 text-gray-600 px-2 py-1 rounded"
                            >
                              {material}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditActivity(activity)}
                      >
                        <Edit2 className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteActivity(activity.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Activity Form */}
      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              {editingActivity ? 'Editar Actividad' : 'Nueva Actividad'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="activityName">Nombre de la Actividad *</Label>
                <Input
                  id="activityName"
                  placeholder="Ej: Exploración del Sistema Solar"
                  value={formData.name || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="activityType">Tipo de Actividad *</Label>
                <select
                  id="activityType"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  value={formData.type || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                >
                  <option value="">Selecciona un tipo</option>
                  {ACTIVITY_TYPES.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="activityDescription">Descripción de la Actividad *</Label>
              <Textarea
                id="activityDescription"
                placeholder="Describe detalladamente cómo se desarrollará la actividad, qué harán los estudiantes, qué papel tendrá el docente..."
                rows={4}
                value={formData.description || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="duration">Duración (minutos) *</Label>
              <Input
                id="duration"
                type="number"
                min="1"
                placeholder="15"
                value={formData.duration || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) || 0 }))}
              />
            </div>

            {/* Materials */}
            <div className="space-y-2">
              <Label>Materiales y Recursos</Label>
              
              {formData.materials && formData.materials.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-2">
                  {formData.materials.map((material, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded"
                    >
                      {material}
                      <button
                        type="button"
                        onClick={() => removeMaterial(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}

              <div className="flex space-x-2">
                <Input
                  placeholder="Ej: Cartulina, marcadores, laptop..."
                  value={materialInput}
                  onChange={(e: unknown) => setMaterialInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addMaterial();
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={addMaterial}
                  disabled={!materialInput.trim()}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={resetForm}>
                Cancelar
              </Button>
              <Button 
                onClick={handleSaveActivity}
                disabled={!formData.name || !formData.description || !formData.type}
              >
                {editingActivity ? 'Actualizar' : 'Agregar'} Actividad
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help Card */}
      <Card className="bg-orange-50 border-orange-200">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <div className="w-5 h-5 rounded-full bg-orange-600 flex items-center justify-center mt-0.5">
              <span className="text-white text-xs font-bold">💡</span>
            </div>
            <div>
              <h4 className="font-medium text-orange-900">Metodología NEM</h4>
              <ul className="text-sm text-orange-800 mt-2 space-y-1">
                <li>• Utiliza metodologías socioconstructivistas (trabajo colaborativo, ABP)</li>
                <li>• Integra los ejes articuladores de manera natural</li>
                <li>• Considera diferentes ritmos y estilos de aprendizaje</li>
                <li>• Incluye momentos de reflexión y metacognición</li>
                <li>• Fomenta la participación activa de todos los estudiantes</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {activities.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="p-8 text-center">
            <div className="space-y-4">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto">
                <Plus className="w-6 h-6 text-gray-400" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">No hay actividades</h3>
                <p className="text-gray-600 text-sm">
                  Agrega al menos una actividad para continuar con tu planeación
                </p>
              </div>
              <Button onClick={() => setShowForm(true)}>
                Crear Primera Actividad
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}