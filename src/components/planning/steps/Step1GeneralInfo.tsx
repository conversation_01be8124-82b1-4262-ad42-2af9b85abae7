import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { usePlanningWizardStore, PlanningStep1 } from '@/stores/planningWizardStore';
import { useAuthStore } from '@/stores/authStore';

const step1Schema = z.object({
  title: z.string().min(1, 'El título es requerido'),
  subject: z.string().min(1, 'La materia es requerida'),
  grade: z.string().min(1, 'El grado es requerido'),
  school: z.string().min(1, 'La escuela es requerida'),
  startDate: z.string().min(1, 'La fecha de inicio es requerida'),
  endDate: z.string().min(1, 'La fecha de fin es requerida'),
  duration: z.number().min(1, 'La duración debe ser mayor a 0'),
});

export function Step1GeneralInfo() {
  const { wizardData, updateStepData, setStepValid } = usePlanningWizardStore();
  const { user } = useAuthStore();
  
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid },
  } = useForm<PlanningStep1>({
    resolver: zodResolver(step1Schema),
    defaultValues: {
      ...wizardData.step1,
      school: wizardData.step1?.school || user?.school || '',
      subject: wizardData.step1?.subject || user?.subject || '',
      grade: wizardData.step1?.grade || user?.grade || '',
    },
    mode: 'onChange',
  });

  const watchedData = watch();

  // Update store when form data changes
  useEffect(() => {
    const timer = setTimeout(() => {
      updateStepData('step1', watchedData);
      setStepValid(1, isValid);
    }, 100);

    return () => clearTimeout(timer);
  }, [watchedData, isValid, updateStepData, setStepValid]);

  const subjectOptions = [
    'Español',
    'Matemáticas',
    'Ciencias Naturales',
    'Historia',
    'Geografía',
    'Formación Cívica y Ética',
    'Educación Física',
    'Artes',
    'Inglés',
    'Tecnología',
  ];

  const gradeOptions = [
    '1° Secundaria',
    '2° Secundaria',
    '3° Secundaria',
  ];

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">
          Información General de la Planeación
        </h2>
        <p className="text-gray-600">
          Establece los datos básicos de tu planeación didáctica
        </p>
      </div>

      <form className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Datos Básicos</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Título de la Planeación *</Label>
                <Input
                  id="title"
                  placeholder="Ej: El Sistema Solar - Explorando nuestro universo"
                  {...register('title')}
                />
                {errors.title && (
                  <p className="text-sm text-red-600">{errors.title.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="school">Escuela *</Label>
                <Input
                  id="school"
                  placeholder="Nombre de la escuela"
                  {...register('school')}
                />
                {errors.school && (
                  <p className="text-sm text-red-600">{errors.school.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="subject">Materia *</Label>
                <select
                  id="subject"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  {...register('subject')}
                >
                  <option value="">Selecciona una materia</option>
                  {subjectOptions.map((subject) => (
                    <option key={subject} value={subject}>
                      {subject}
                    </option>
                  ))}
                </select>
                {errors.subject && (
                  <p className="text-sm text-red-600">{errors.subject.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="grade">Grado *</Label>
                <select
                  id="grade"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  {...register('grade')}
                >
                  <option value="">Selecciona un grado</option>
                  {gradeOptions.map((grade) => (
                    <option key={grade} value={grade}>
                      {grade}
                    </option>
                  ))}
                </select>
                {errors.grade && (
                  <p className="text-sm text-red-600">{errors.grade.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Time Planning */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Planificación Temporal</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">Fecha de Inicio *</Label>
                <Input
                  id="startDate"
                  type="date"
                  {...register('startDate')}
                />
                {errors.startDate && (
                  <p className="text-sm text-red-600">{errors.startDate.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="endDate">Fecha de Fin *</Label>
                <Input
                  id="endDate"
                  type="date"
                  {...register('endDate')}
                />
                {errors.endDate && (
                  <p className="text-sm text-red-600">{errors.endDate.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="duration">Duración (minutos) *</Label>
                <Input
                  id="duration"
                  type="number"
                  min="1"
                  placeholder="50"
                  {...register('duration', { valueAsNumber: true })}
                />
                {errors.duration && (
                  <p className="text-sm text-red-600">{errors.duration.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Help Card */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <div className="w-5 h-5 rounded-full bg-blue-600 flex items-center justify-center mt-0.5">
                <span className="text-white text-xs font-bold">💡</span>
              </div>
              <div>
                <h4 className="font-medium text-blue-900">Consejos para esta etapa</h4>
                <ul className="text-sm text-blue-800 mt-2 space-y-1">
                  <li>• Usa títulos descriptivos que reflejen el tema principal</li>
                  <li>• La duración debe considerar el ritmo de aprendizaje de tus estudiantes</li>
                  <li>• Planifica fechas realistas considerando días festivos y actividades escolares</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}