import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { usePlanningWizardStore, PlanningStep2 } from '@/stores/planningWizardStore';

const step2Schema = z.object({
  formativeField: z.string().min(1, 'Debes seleccionar un Campo Formativo'),
  articulatingAxes: z.array(z.string()).min(1, 'Debes seleccionar al menos un Eje Articulador'),
});

const FORMATIVE_FIELDS = [
  {
    id: 'lenguajes',
    name: 'Lenguajes',
    description: 'Desarrollo de habilidades comunicativas en diversas modalidades',
    subjects: ['Español', 'Inglés', 'Lenguas Indígenas'],
  },
  {
    id: 'saberes-cientifico',
    name: 'Saberes y Pensamiento Científico',
    description: 'Construcción del conocimiento científico y matemático',
    subjects: ['Matemáticas', 'Ciencias Naturales', 'Tecnología'],
  },
  {
    id: 'etica-naturaleza',
    name: 'Ética, Naturaleza y Sociedades',
    description: 'Formación ciudadana y comprensión del entorno',
    subjects: ['Historia', 'Geografía', 'Formación Cívica y Ética'],
  },
  {
    id: 'humano-comunitario',
    name: 'De lo Humano y lo Comunitario',
    description: 'Desarrollo socioemocional y artístico',
    subjects: ['Artes', 'Educación Física', 'Educación Socioemocional'],
  },
];

const ARTICULATING_AXES = [
  {
    id: 'inclusion',
    name: 'Inclusión',
    description: 'Atención a la diversidad y eliminación de barreras de aprendizaje',
  },
  {
    id: 'pensamiento-critico',
    name: 'Pensamiento Crítico',
    description: 'Desarrollo del razonamiento y análisis reflexivo',
  },
  {
    id: 'interculturalidad',
    name: 'Interculturalidad Crítica',
    description: 'Valoración y diálogo entre culturas',
  },
  {
    id: 'igualdad-genero',
    name: 'Igualdad de Género',
    description: 'Promoción de la equidad y eliminación de estereotipos',
  },
  {
    id: 'vida-saludable',
    name: 'Vida Saludable',
    description: 'Fomento del bienestar físico, mental y social',
  },
  {
    id: 'apropiacion-culturas',
    name: 'Apropiación de las Culturas a través de la Lectura y la Escritura',
    description: 'Desarrollo de la literacidad y el patrimonio cultural',
  },
  {
    id: 'artes-experiencias',
    name: 'Artes y Experiencias Estéticas',
    description: 'Expresión artística y sensibilidad estética',
  },
];

export function Step2FieldsAndAxes() {
  const { wizardData, updateStepData, setStepValid } = usePlanningWizardStore();

  const defaultValues: PlanningStep2 = {
    formativeField: wizardData.step2?.formativeField || '',
    articulatingAxes: wizardData.step2?.articulatingAxes || [],
  };

  const {
    register,
    watch,
    setValue,
    reset,
    formState: { isValid },
  } = useForm<PlanningStep2>({
    resolver: zodResolver(step2Schema),
    defaultValues,
    mode: 'onChange',
  });

  // Reset form when wizard data changes
  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const watchedData = watch();
  const selectedField = watch('formativeField');
  const selectedAxes = watch('articulatingAxes') || [];

  // Update store when form data changes
  useEffect(() => {
    const timer = setTimeout(() => {
      updateStepData('step2', watchedData);
      setStepValid(2, isValid);
    }, 100);

    return () => clearTimeout(timer);
  }, [watchedData, isValid, updateStepData, setStepValid]);

  const handleFieldChange = (fieldId: string) => {
    setValue('formativeField', fieldId, { shouldValidate: true });
  };

  const handleAxesChange = (axisId: string, checked: boolean) => {
    const currentAxes = selectedAxes || [];
    let newAxes: string[];

    if (checked) {
      newAxes = [...currentAxes, axisId];
    } else {
      newAxes = currentAxes.filter(id => id !== axisId);
    }

    setValue('articulatingAxes', newAxes, { shouldValidate: true });
  };

  // Get subject from previous step for recommendations
  const currentSubject = wizardData.step1?.subject;
  const recommendedField = FORMATIVE_FIELDS.find(field => 
    field.subjects.includes(currentSubject || '')
  );

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">
          Campo Formativo y Ejes Articuladores
        </h2>
        <p className="text-gray-600">
          Selecciona el marco curricular que guiará tu planeación
        </p>
        {/* Debug info */}
        <div className="text-xs text-gray-500 bg-gray-100 p-2 rounded">
          Campo: {selectedField || 'Ninguno'} | Ejes: {selectedAxes.length} | Válido: {isValid ? 'Sí' : 'No'}
        </div>
      </div>

      <form className="space-y-6">
        {/* Formative Fields */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Campo Formativo</CardTitle>
            <p className="text-sm text-gray-600">
              Selecciona el campo formativo principal para tu planeación
              {recommendedField && (
                <span className="block mt-1 text-blue-600 font-medium">
                  Recomendado para {currentSubject}: {recommendedField.name}
                </span>
              )}
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {FORMATIVE_FIELDS.map((field) => (
                <div
                  key={field.id}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    selectedField === field.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleFieldChange(field.id)}
                >
                  <div className="flex items-start space-x-3">
                    <input
                      type="radio"
                      id={field.id}
                      value={field.id}
                      checked={selectedField === field.id}
                      {...register('formativeField')}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{field.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{field.description}</p>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {field.subjects.map((subject) => (
                          <span
                            key={subject}
                            className="px-2 py-1 bg-gray-100 text-xs rounded-full text-gray-700"
                          >
                            {subject}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Articulating Axes */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Ejes Articuladores</CardTitle>
            <p className="text-sm text-gray-600">
              Selecciona los ejes transversales que integrarás en tu planeación (mínimo 1)
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              {ARTICULATING_AXES.map((axis) => (
                <div
                  key={axis.id}
                  className={`p-4 border rounded-lg transition-all cursor-pointer ${
                    selectedAxes.includes(axis.id)
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => {
                    const isCurrentlySelected = selectedAxes.includes(axis.id);
                    handleAxesChange(axis.id, !isCurrentlySelected);
                  }}
                >
                  <div className="flex items-start space-x-3" onClick={(e) => e.stopPropagation()}>
                    <Checkbox
                      id={axis.id}
                      checked={selectedAxes.includes(axis.id)}
                      onCheckedChange={(checked) =>
                        handleAxesChange(axis.id, checked as boolean)
                      }
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <Label htmlFor={axis.id} className="font-semibold text-gray-900 cursor-pointer">
                        {axis.name}
                      </Label>
                      <p className="text-sm text-gray-600 mt-1">{axis.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Help Card */}
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <div className="w-5 h-5 rounded-full bg-green-600 flex items-center justify-center mt-0.5">
                <span className="text-white text-xs font-bold">✓</span>
              </div>
              <div>
                <h4 className="font-medium text-green-900">¿Cómo elegir correctamente?</h4>
                <ul className="text-sm text-green-800 mt-2 space-y-1">
                  <li>• El Campo Formativo debe alinearse con tu materia principal</li>
                  <li>• Los Ejes Articuladores deben integrarse naturalmente en las actividades</li>
                  <li>• Considera las características y necesidades de tus estudiantes</li>
                  <li>• Puedes integrar múltiples ejes para una experiencia más rica</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}