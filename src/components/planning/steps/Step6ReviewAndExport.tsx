import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '../../ui/tabs';
import { 
  CheckCircle, 
  AlertCircle
} from 'lucide-react';
import { usePlanningWizardStore } from '../../../stores/planningWizardStore';
import { ExportPanel } from '../ExportPanel';
import { ValidationPanel } from '../ValidationPanel';

export function Step6ReviewAndExport() {
  const { wizardData } = usePlanningWizardStore();

  const getCompletionStatus = () => {
    const steps = [
      { name: 'Información General', complete: !!wizardData.step1?.title },
      { name: 'Campo y Ejes', complete: !!wizardData.step2?.formativeField },
      { name: 'PDA', complete: !!wizardData.step3?.pda },
      { name: 'Actividades', complete: (wizardData.step4?.activities?.length || 0) > 0 },
      { name: 'Evaluación', complete: (wizardData.step5?.evaluation?.instruments?.length || 0) > 0 },
    ];
    
    const completed = steps.filter(s => s.complete).length;
    return { steps, completed, total: steps.length };
  };

  const { steps, completed, total } = getCompletionStatus();
  const completionPercentage = Math.round((completed / total) * 100);

  // Prepare data for export
  const exportData = {
    title: wizardData.step1?.title || '',
    subject: wizardData.step1?.subject || '',
    grade: wizardData.step1?.grade || '',
    school: wizardData.step1?.school || '',
    startDate: wizardData.step1?.startDate || '',
    endDate: wizardData.step1?.endDate || '',
    duration: wizardData.step1?.duration?.toString() || '',
    objectives: wizardData.step3?.objectives || [],
    content: {
      formativeField: wizardData.step2?.formativeField || '',
      articulatingAxes: wizardData.step2?.articulatingAxes || [],
      learningProcess: wizardData.step3 || {},
      activities: wizardData.step4?.activities || [],
      evaluation: wizardData.step5?.evaluation || {},
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">
          Revisión y Exportación
        </h2>
        <p className="text-gray-600">
          Revisa tu planeación completa y expórtala en formato oficial SEP
        </p>
      </div>

      {/* Completion Status */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span>Estado de Completitud</span>
            <Badge variant={completed === total ? 'default' : 'secondary'}>
              {completionPercentage}% Completo
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
            {steps.map((step, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border text-center ${
                  step.complete 
                    ? 'bg-green-50 border-green-200 text-green-800'
                    : 'bg-gray-50 border-gray-200 text-gray-600'
                }`}
              >
                <div className="flex flex-col items-center space-y-1">
                  {step.complete ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-gray-400" />
                  )}
                  <span className="text-xs font-medium">{step.name}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="preview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="preview">Vista Previa</TabsTrigger>
          <TabsTrigger value="validation">Validación IA</TabsTrigger>
          <TabsTrigger value="export">Exportar</TabsTrigger>
        </TabsList>

        {/* Preview Tab */}
        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Vista Previa de la Planeación</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* General Info */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Información General</h3>
                <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                  <p><span className="font-medium">Título:</span> {wizardData.step1?.title || 'No especificado'}</p>
                  <p><span className="font-medium">Escuela:</span> {wizardData.step1?.school || 'No especificada'}</p>
                  <p><span className="font-medium">Grado:</span> {wizardData.step1?.grade || 'No especificado'}</p>
                  <p><span className="font-medium">Materia:</span> {wizardData.step1?.subject || 'No especificada'}</p>
                  <p><span className="font-medium">Duración:</span> {wizardData.step1?.duration || 0} minutos</p>
                </div>
              </div>

              {/* Formative Field and Axes */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Campo Formativo y Ejes</h3>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="mb-2">
                    <span className="font-medium">Campo Formativo:</span> {wizardData.step2?.formativeField || 'No seleccionado'}
                  </p>
                  <div>
                    <span className="font-medium">Ejes Articuladores:</span>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {wizardData.step2?.articulatingAxes?.map((axis) => (
                        <Badge key={axis} variant="outline">{axis}</Badge>
                      )) || <span className="text-gray-500">Ninguno seleccionado</span>}
                    </div>
                  </div>
                </div>
              </div>

              {/* Learning Process */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Proceso de Desarrollo</h3>
                <div className="bg-purple-50 p-4 rounded-lg space-y-2">
                  <p><span className="font-medium">PDA:</span> {wizardData.step3?.pda || 'No seleccionado'}</p>
                  <div>
                    <span className="font-medium">Objetivos:</span>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      {wizardData.step3?.objectives?.map((obj) => (
                        <li key={obj} className="text-sm">{obj}</li>
                      )) || <li className="text-gray-500">No hay objetivos definidos</li>}
                    </ul>
                  </div>
                </div>
              </div>

              {/* Activities */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Actividades</h3>
                <div className="space-y-3">
                  {wizardData.step4?.activities?.map((activity) => (
                    <div key={activity.id} className="bg-orange-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{activity.name}</h4>
                        <div className="flex space-x-2">
                          <Badge variant="outline">{activity.type}</Badge>
                          <Badge variant="outline">{activity.duration} min</Badge>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700">{activity.description}</p>
                    </div>
                  )) || <div className="bg-gray-50 p-4 rounded-lg text-gray-500">No hay actividades definidas</div>}
                </div>
              </div>

              {/* Evaluation */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Evaluación</h3>
                <div className="bg-green-50 p-4 rounded-lg space-y-2">
                  <div>
                    <span className="font-medium">Instrumentos:</span>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {wizardData.step5?.evaluation?.instruments?.map((inst) => (
                        <Badge key={inst} variant="outline">{inst}</Badge>
                      )) || <span className="text-gray-500">No hay instrumentos seleccionados</span>}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium">Criterios:</span>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      {wizardData.step5?.evaluation?.criteria?.map((crit) => (
                        <li key={crit} className="text-sm">{crit}</li>
                      )) || <li className="text-gray-500">No hay criterios definidos</li>}
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Validation Tab */}
        <TabsContent value="validation" className="space-y-4">
          <ValidationPanel />
        </TabsContent>

        {/* Export Tab */}
        <TabsContent value="export" className="space-y-4">
          <ExportPanel planningData={exportData} />
          
          {completed < total && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-800">Planeación Incompleta</h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      Completa todos los pasos para habilitar la exportación en formato oficial.
                      Faltan {total - completed} secciones por completar.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}