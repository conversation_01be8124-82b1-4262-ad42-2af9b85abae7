import React from 'react';
import { Link, useLocation } from 'wouter';
import { Home, MessageSquare, Wand2, CheckCircle2, BookOpen, BookTemplate as FileTemplate, Eye, LogOut, User, PlusCircle, Brain } from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { useAuthStore } from '../../stores/authStore';
import { authApi } from '../../lib/api';
import { useToast } from '../../hooks/useToast';

const navItems = [
  { path: '/', label: 'Inicio', icon: Home },
  { path: '/planning', label: 'Nueva Planeación', icon: PlusCircle },
  { path: '/chat', label: 'Chat IA', icon: MessageSquare },
  { path: '/generator', label: 'Generador', icon: Wand2 },
  { path: '/validator', label: 'Validador', icon: CheckCircle2 },
  { path: '/knowledge-base', label: 'Base SEP', icon: Brain },
  { path: '/resources', label: 'Recursos', icon: BookOpen },
  { path: '/templates', label: 'Plantillas', icon: FileTemplate },
  { path: '/preview', label: 'Vista Previa', icon: Eye },
];

export function Navigation() {
  const [location] = useLocation();
  const { user, logout } = useAuthStore();
  const { toast } = useToast();

  const handleLogout = async () => {
    try {
      await authApi.logout();
      logout();
      toast({
        title: 'Sesión cerrada',
        description: 'Has cerrado sesión exitosamente',
      });
    } catch (error) {
      console.error('Logout error:', error);
      logout(); // Still logout locally even if API fails
      toast({
        title: 'Sesión cerrada',
        description: 'Has cerrado sesión exitosamente',
      });
    }
  };

  return (
    <nav className="bg-white border-b border-gray-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/">
              <div className="flex items-center space-x-3 cursor-pointer">
                <div className="w-10 h-10 bg-gradient-to-r from-green-600 to-blue-600 rounded-xl flex items-center justify-center">
                  <BookOpen className="w-6 h-6 text-white" />
                </div>
                <div className="hidden sm:block">
                  <h1 className="text-xl font-bold text-gray-900">
                    Planeación NEM
                  </h1>
                  <p className="text-xs text-gray-500">Nueva Escuela Mexicana</p>
                </div>
              </div>
            </Link>
          </div>

          {/* Navigation Links - Desktop */}
          <div className="hidden lg:flex items-center space-x-1">
            {navItems.slice(0, 7).map((item) => {
              const Icon = item.icon;
              const isActive = location === item.path;
              
              return (
                <Link key={item.path} href={item.path}>
                  <Button
                    variant={isActive ? 'default' : 'ghost'}
                    size="sm"
                    className="flex items-center space-x-2"
                  >
                    <Icon className="w-4 h-4" />
                    <span className="hidden xl:inline">{item.label}</span>
                  </Button>
                </Link>
              );
            })}
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {/* User Info */}
            <div className="hidden md:flex items-center space-x-3">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">
                  {user?.firstName} {user?.lastName}
                </p>
                <p className="text-xs text-gray-500">
                  {user?.school || 'Docente NEM'}
                </p>
              </div>
              <div className="w-8 h-8 bg-gradient-to-r from-green-100 to-blue-100 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-green-600" />
              </div>
            </div>

            {/* Logout Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="flex items-center space-x-2"
            >
              <LogOut className="w-4 h-4" />
              <span className="hidden sm:inline">Salir</span>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="lg:hidden pb-3 pt-2">
          <div className="flex overflow-x-auto space-x-2 pb-2">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = location === item.path;
              
              return (
                <Link key={item.path} href={item.path}>
                  <Button
                    variant={isActive ? 'default' : 'ghost'}
                    size="sm"
                    className="flex items-center space-x-2 whitespace-nowrap"
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-xs">{item.label}</span>
                  </Button>
                </Link>
              );
            })}
          </div>
        </div>
      </div>
    </nav>
  );
}