import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Wand2, 
  Target, 
  CheckCircle, 
  BookOpen, 
  Download,
  Copy,
  RefreshCw,
  Sparkles
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { aiApi } from '@/lib/api';
import { usePlanningWizardStore } from '@/stores/planningWizardStore';

interface GeneratedContent {
  type: string;
  content: any;
  timestamp: Date;
}

export function ContentGenerator() {
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent[]>([]);
  const [isGenerating, setIsGenerating] = useState<string | null>(null);
  const { wizardData } = usePlanningWizardStore();
  const { toast } = useToast();

  const generateContent = async (type: 'objectives' | 'activities' | 'evaluation' | 'projects') => {
    setIsGenerating(type);
    try {
      const context = {
        formativeField: wizardData.step2?.formativeField || '',
        grade: wizardData.step1?.grade || '',
        subject: wizardData.step1?.subject || '',
        articulatingAxes: wizardData.step2?.articulatingAxes || [],
        pda: wizardData.step3?.pda || '',
        objectives: wizardData.step3?.objectives || [],
        activities: wizardData.step4?.activities || [],
      };

      const result = await aiApi.generateContent(type, context);
      
      if (result.data?.content) {
        const newContent: GeneratedContent = {
          type,
          content: result.data.content,
          timestamp: new Date(),
        };
        
        setGeneratedContent(prev => [newContent, ...prev]);
        
        toast({
          title: 'Contenido generado',
          description: `Se ha generado ${type} exitosamente con IA`,
        });
      }
    } catch (error) {
      console.error('Error generating content:', error);
      console.error('Error details:', {
        message: error?.message,
        response: error?.response?.data,
        status: error?.response?.status,
        type,
        context
      });

      toast({
        variant: 'destructive',
        title: 'Error al generar',
        description: `No se pudo generar el contenido. ${error?.response?.data?.error || error?.message || 'Intenta nuevamente.'}`,
      });
    } finally {
      setIsGenerating(null);
    }
  };

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content);
    toast({
      title: 'Copiado',
      description: 'Contenido copiado al portapapeles',
    });
  };

  const generatorOptions = [
    {
      id: 'objectives',
      title: 'Objetivos de Aprendizaje',
      description: 'Genera objetivos específicos alineados con PDAs y campo formativo',
      icon: Target,
      color: 'from-blue-500 to-blue-600',
      requirements: ['Campo Formativo', 'PDA'],
    },
    {
      id: 'activities',
      title: 'Actividades Didácticas',
      description: 'Crea actividades socioconstructivistas con metodología NEM',
      icon: Wand2,
      color: 'from-green-500 to-green-600',
      requirements: ['Objetivos', 'Campo Formativo'],
    },
    {
      id: 'evaluation',
      title: 'Instrumentos de Evaluación',
      description: 'Diseña rúbricas, listas de cotejo y criterios de evaluación',
      icon: CheckCircle,
      color: 'from-purple-500 to-purple-600',
      requirements: ['Actividades', 'Objetivos'],
    },
    {
      id: 'projects',
      title: 'Proyectos Comunitarios',
      description: 'Genera proyectos de aula, escuela y comunidad',
      icon: BookOpen,
      color: 'from-orange-500 to-orange-600',
      requirements: ['Campo Formativo', 'Ejes Articuladores'],
    },
  ];

  const getContentPreview = (content: any, type: string) => {
    switch (type) {
      case 'objectives':
        return Array.isArray(content.objectives) 
          ? content.objectives.slice(0, 3).join('\n• ')
          : content.toString().slice(0, 200);
      
      case 'activities':
        return Array.isArray(content.activities)
          ? content.activities.slice(0, 2).map((act: any) => act.name || act).join('\n• ')
          : content.toString().slice(0, 200);
      
      case 'evaluation':
        return Array.isArray(content.criteria)
          ? content.criteria.slice(0, 3).join('\n• ')
          : content.toString().slice(0, 200);
      
      default:
        return content.toString().slice(0, 200);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center justify-center space-x-2">
          <Sparkles className="w-6 h-6 text-purple-600" />
          <span>Generador de Contenido IA</span>
        </h2>
        <p className="text-gray-600">
          Genera contenido educativo automáticamente con inteligencia artificial
        </p>
      </div>

      <Tabs defaultValue="generate" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="generate">Generar Contenido</TabsTrigger>
          <TabsTrigger value="history">Historial ({generatedContent.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-6">
          {/* Generator Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {generatorOptions.map((option) => {
              const Icon = option.icon;
              const isGeneratingThis = isGenerating === option.id;
              
              return (
                <Card key={option.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${option.color} flex items-center justify-center`}>
                        <Icon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{option.title}</h3>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-gray-600">{option.description}</p>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Requiere:</h4>
                      <div className="flex flex-wrap gap-1">
                        {option.requirements.map((req, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {req}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <Button
                      onClick={() => generateContent(option.id as any)}
                      disabled={isGeneratingThis || isGenerating !== null}
                      className="w-full flex items-center space-x-2"
                    >
                      {isGeneratingThis ? (
                        <>
                          <RefreshCw className="w-4 h-4 animate-spin" />
                          <span>Generando...</span>
                        </>
                      ) : (
                        <>
                          <Wand2 className="w-4 h-4" />
                          <span>Generar</span>
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Context Information */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader>
              <CardTitle className="text-lg text-blue-900">Contexto Actual</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h4 className="font-medium text-blue-900">Campo Formativo</h4>
                  <p className="text-sm text-blue-700">
                    {wizardData.step2?.formativeField || 'No seleccionado'}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-blue-900">Materia y Grado</h4>
                  <p className="text-sm text-blue-700">
                    {wizardData.step1?.subject || 'No especificado'} - {wizardData.step1?.grade || 'No especificado'}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-blue-900">Ejes Articuladores</h4>
                  <p className="text-sm text-blue-700">
                    {wizardData.step2?.articulatingAxes?.length || 0} seleccionados
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          {generatedContent.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Wand2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No hay contenido generado
                </h3>
                <p className="text-gray-600">
                  Genera tu primer contenido usando las opciones de la pestaña anterior
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {generatedContent.map((item, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg capitalize flex items-center space-x-2">
                        <Badge variant="outline">{item.type}</Badge>
                        <span>Contenido Generado</span>
                      </CardTitle>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(JSON.stringify(item.content, null, 2))}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500">
                      Generado el {item.timestamp.toLocaleString()}
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <pre className="text-sm text-gray-800 whitespace-pre-wrap">
                        {getContentPreview(item.content, item.type)}
                        {getContentPreview(item.content, item.type).length >= 200 && '...'}
                      </pre>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}