import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  MessageSquare, 
  Send, 
  Bot, 
  User, 
  Wand2, 
  Lightbulb,
  BookOpen,
  Target,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { aiApi } from '@/lib/api';
import { usePlanningWizardStore } from '@/stores/planningWizardStore';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  context?: any;
}

interface AIAssistantProps {
  context?: {
    step?: number;
    formativeField?: string;
    subject?: string;
    grade?: string;
    planningId?: string;
  };
  onContentGenerated?: (content: any) => void;
}

export function AIAssistant({ context, onContentGenerated }: AIAssistantProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const { wizardData } = usePlanningWizardStore();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize with welcome message
  useEffect(() => {
    const welcomeMessage: Message = {
      id: 'welcome',
      role: 'assistant',
      content: `¡Hola! Soy tu asistente pedagógico especializado en la Nueva Escuela Mexicana. 

Puedo ayudarte con:
• Crear objetivos de aprendizaje alineados con PDAs
• Diseñar actividades socioconstructivistas
• Generar instrumentos de evaluación formativa
• Integrar ejes articuladores en tu planeación
• Validar coherencia con el marco NEM

${context?.formativeField ? `Veo que estás trabajando en el campo formativo: **${context.formativeField}**` : ''}
${context?.subject ? ` para la materia de **${context.subject}**` : ''}
${context?.grade ? ` en **${context.grade}**` : ''}

¿En qué puedo ayudarte hoy?`,
      timestamp: new Date(),
    };
    setMessages([welcomeMessage]);
  }, [context]);

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputMessage,
      timestamp: new Date(),
      context,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const result = await aiApi.chat(inputMessage, {
        ...context,
        currentWizardData: wizardData,
      });

      // Handle axios response structure: result.data contains the backend response
      if (result.data?.response) {
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: result.data.response,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, assistantMessage]);
      } else {
        throw new Error('No response received from AI');
      }
    } catch (error) {
      console.error('Chat error:', error);
      console.error('Error details:', {
        message: error?.message,
        response: error?.response?.data,
        status: error?.response?.status,
        inputMessage,
        context
      });

      toast({
        variant: 'destructive',
        title: 'Error en el chat',
        description: `No se pudo enviar el mensaje. ${error?.response?.data?.error || error?.message || 'Intenta nuevamente.'}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const generateContent = async (type: 'objectives' | 'activities' | 'evaluation') => {
    setIsGenerating(true);
    try {
      const contextData = {
        formativeField: context?.formativeField || wizardData.step2?.formativeField || '',
        grade: context?.grade || wizardData.step1?.grade || '',
        subject: context?.subject || wizardData.step1?.subject || '',
        articulatingAxes: wizardData.step2?.articulatingAxes || [],
        pda: wizardData.step3?.pda || '',
        objectives: wizardData.step3?.objectives || [],
        activities: wizardData.step4?.activities || [],
      };

      const result = await aiApi.generateContent(type, contextData);
      
      if (result.data?.content) {
        const content = result.data.content;
        
        // Add generated content as assistant message
        const assistantMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content: `He generado contenido para **${type}** basado en tu contexto actual:\n\n${JSON.stringify(content, null, 2)}`,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, assistantMessage]);

        // Notify parent component
        if (onContentGenerated) {
          onContentGenerated({ type, content });
        }

        toast({
          title: 'Contenido generado',
          description: `Se ha generado contenido para ${type} exitosamente`,
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error al generar',
        description: 'No se pudo generar el contenido. Intenta nuevamente.',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const quickPrompts = [
    {
      text: '¿Cómo integro el pensamiento crítico?',
      icon: Lightbulb,
      category: 'Ejes Articuladores',
    },
    {
      text: 'Sugiere actividades socioconstructivistas',
      icon: Target,
      category: 'Metodología',
    },
    {
      text: 'Crea una rúbrica de evaluación',
      icon: CheckCircle,
      category: 'Evaluación',
    },
    {
      text: 'Explica los campos formativos',
      icon: BookOpen,
      category: 'Marco NEM',
    },
  ];

  return (
    <div className="flex flex-col h-full max-h-[600px]">
      {/* Header */}
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center space-x-2">
          <Bot className="w-5 h-5 text-blue-600" />
          <span>Asistente IA Pedagógico</span>
          {context?.step && (
            <Badge variant="outline">Paso {context.step}</Badge>
          )}
        </CardTitle>
        {context && (
          <div className="flex flex-wrap gap-2 mt-2">
            {context.formativeField && (
              <Badge variant="secondary">{context.formativeField}</Badge>
            )}
            {context.subject && (
              <Badge variant="outline">{context.subject}</Badge>
            )}
            {context.grade && (
              <Badge variant="outline">{context.grade}</Badge>
            )}
          </div>
        )}
      </CardHeader>

      {/* Quick Actions */}
      <div className="px-6 pb-4">
        <div className="flex flex-wrap gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => generateContent('objectives')}
            disabled={isGenerating}
            className="flex items-center space-x-1"
          >
            <Wand2 className="w-3 h-3" />
            <span>Objetivos</span>
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => generateContent('activities')}
            disabled={isGenerating}
            className="flex items-center space-x-1"
          >
            <Wand2 className="w-3 h-3" />
            <span>Actividades</span>
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => generateContent('evaluation')}
            disabled={isGenerating}
            className="flex items-center space-x-1"
          >
            <Wand2 className="w-3 h-3" />
            <span>Evaluación</span>
          </Button>
        </div>
      </div>

      {/* Messages */}
      <CardContent className="flex-1 overflow-y-auto space-y-4 max-h-96">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex items-start space-x-3 ${
              message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''
            }`}
          >
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              message.role === 'user' 
                ? 'bg-blue-100 text-blue-600' 
                : 'bg-green-100 text-green-600'
            }`}>
              {message.role === 'user' ? (
                <User className="w-4 h-4" />
              ) : (
                <Bot className="w-4 h-4" />
              )}
            </div>
            <div className={`flex-1 max-w-xs lg:max-w-md ${
              message.role === 'user' ? 'text-right' : ''
            }`}>
              <div className={`p-3 rounded-lg ${
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}>
                <div className="text-sm whitespace-pre-wrap">
                  {message.content}
                </div>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center">
              <Bot className="w-4 h-4" />
            </div>
            <div className="flex-1">
              <div className="bg-gray-100 p-3 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm text-gray-600">Pensando...</span>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </CardContent>

      {/* Quick Prompts */}
      <div className="px-6 py-2">
        <div className="grid grid-cols-2 gap-2">
          {quickPrompts.map((prompt, index) => (
            <Button
              key={index}
              variant="ghost"
              size="sm"
              onClick={() => setInputMessage(prompt.text)}
              className="text-left justify-start h-auto p-2"
            >
              <div className="flex items-center space-x-2">
                <prompt.icon className="w-3 h-3" />
                <div>
                  <div className="text-xs font-medium">{prompt.category}</div>
                  <div className="text-xs text-gray-600 truncate">
                    {prompt.text}
                  </div>
                </div>
              </div>
            </Button>
          ))}
        </div>
      </div>

      {/* Input */}
      <div className="p-6 pt-4 border-t">
        <div className="flex space-x-2">
          <Textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder="Pregunta sobre planeación, metodología NEM, actividades..."
            className="flex-1 min-h-[40px] max-h-[100px]"
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
              }
            }}
          />
          <Button
            onClick={sendMessage}
            disabled={isLoading || !inputMessage.trim()}
            className="flex items-center space-x-2"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}