import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  TrendingUp,
  BookOpen,
  Target,
  Users,
  Lightbulb
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { aiApi } from '@/lib/api';
import { usePlanningWizardStore } from '@/stores/planningWizardStore';

interface ValidationResult {
  score: number;
  strengths: string[];
  improvements: string[];
  suggestions: string[];
  categories: {
    nemAlignment: number;
    pedagogicalCoherence: number;
    inclusivity: number;
    articulatingAxes: number;
  };
}

export function ValidationPanel() {
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const { wizardData } = usePlanningWizardStore();
  const { toast } = useToast();

  const validatePlanning = async () => {
    setIsValidating(true);
    try {
      const planningData = {
        title: wizardData.step1?.title,
        subject: wizardData.step1?.subject,
        grade: wizardData.step1?.grade,
        formativeField: wizardData.step2?.formativeField,
        articulatingAxes: wizardData.step2?.articulatingAxes,
        learningProcess: wizardData.step3,
        activities: wizardData.step4?.activities,
        evaluation: wizardData.step5?.evaluation,
      };

      const result = await aiApi.validatePlanning(planningData);
      
      if (result.data?.validation) {
        setValidationResult(result.data.validation);
        toast({
          title: 'Validación completada',
          description: 'Tu planeación ha sido analizada por IA',
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error en validación',
        description: 'No se pudo validar la planeación',
      });
    } finally {
      setIsValidating(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  const validationCategories = [
    {
      key: 'nemAlignment',
      label: 'Alineación NEM',
      icon: BookOpen,
      description: 'Coherencia con marcos curriculares oficiales',
    },
    {
      key: 'pedagogicalCoherence',
      label: 'Coherencia Pedagógica',
      icon: Target,
      description: 'Alineación objetivos-actividades-evaluación',
    },
    {
      key: 'inclusivity',
      label: 'Inclusión y Diversidad',
      icon: Users,
      description: 'Atención a la diversidad y accesibilidad',
    },
    {
      key: 'articulatingAxes',
      label: 'Ejes Articuladores',
      icon: Lightbulb,
      description: 'Integración de ejes transversales',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center justify-center space-x-2">
          <CheckCircle className="w-6 h-6 text-green-600" />
          <span>Validador NEM con IA</span>
        </h2>
        <p className="text-gray-600">
          Valida la coherencia y alineación de tu planeación con la Nueva Escuela Mexicana
        </p>
      </div>

      {/* Validation Trigger */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Análisis de Coherencia</span>
            <Button
              onClick={validatePlanning}
              disabled={isValidating}
              className="flex items-center space-x-2"
            >
              <CheckCircle className="w-4 h-4" />
              <span>{isValidating ? 'Validando...' : 'Validar con IA'}</span>
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600">
            La IA analizará tu planeación completa y proporcionará una puntuación de coherencia
            junto con recomendaciones específicas para mejorar la alineación con la NEM.
          </p>
        </CardContent>
      </Card>

      {/* Validation Results */}
      {validationResult && (
        <div className="space-y-6">
          {/* Overall Score */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Puntuación General</span>
                <Badge 
                  variant={getScoreBadgeVariant(validationResult.score)}
                  className="text-lg px-4 py-2"
                >
                  {validationResult.score}/100
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Progress value={validationResult.score} className="h-3" />
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {validationCategories.map((category) => {
                    const Icon = category.icon;
                    const score = validationResult.categories[category.key as keyof typeof validationResult.categories];
                    
                    return (
                      <div key={category.key} className="text-center space-y-2">
                        <div className="flex items-center justify-center space-x-2">
                          <Icon className="w-4 h-4 text-gray-600" />
                          <span className="text-sm font-medium">{category.label}</span>
                        </div>
                        <div className={`text-2xl font-bold ${getScoreColor(score)}`}>
                          {score}
                        </div>
                        <Progress value={score} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Detailed Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Strengths */}
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="text-green-800 flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5" />
                  <span>Fortalezas</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {validationResult.strengths.map((strength, index) => (
                    <li key={index} className="text-sm text-green-700 flex items-start space-x-2">
                      <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>{strength}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Improvements */}
            <Card className="border-yellow-200 bg-yellow-50">
              <CardHeader>
                <CardTitle className="text-yellow-800 flex items-center space-x-2">
                  <AlertTriangle className="w-5 h-5" />
                  <span>Áreas de Mejora</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {validationResult.improvements.map((improvement, index) => (
                    <li key={index} className="text-sm text-yellow-700 flex items-start space-x-2">
                      <AlertTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>{improvement}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Suggestions */}
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-blue-800 flex items-center space-x-2">
                  <Lightbulb className="w-5 h-5" />
                  <span>Sugerencias</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {validationResult.suggestions.map((suggestion, index) => (
                    <li key={index} className="text-sm text-blue-700 flex items-start space-x-2">
                      <Lightbulb className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Category Details */}
          <Card>
            <CardHeader>
              <CardTitle>Análisis Detallado por Categoría</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {validationCategories.map((category) => {
                  const Icon = category.icon;
                  const score = validationResult.categories[category.key as keyof typeof validationResult.categories];
                  
                  return (
                    <div key={category.key} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Icon className="w-5 h-5 text-gray-600" />
                          <h3 className="font-medium">{category.label}</h3>
                        </div>
                        <Badge variant={getScoreBadgeVariant(score)}>
                          {score}/100
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{category.description}</p>
                      <Progress value={score} className="h-2" />
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* No Results State */}
      {!validationResult && !isValidating && (
        <Card className="border-dashed">
          <CardContent className="p-8 text-center">
            <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Análisis de Coherencia Pendiente
            </h3>
            <p className="text-gray-600 mb-4">
              Haz clic en "Validar con IA" para obtener un análisis detallado de tu planeación
            </p>
            <Button onClick={validatePlanning} disabled={isValidating}>
              Iniciar Validación
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}