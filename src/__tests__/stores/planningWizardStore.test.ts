import { describe, it, expect, beforeEach } from 'vitest';
import { usePlanningWizardStore } from '../../stores/planningWizardStore';

// Helper to get a fresh store instance
const getStore = () => usePlanningWizardStore.getState();

describe('PlanningWizardStore', () => {
  beforeEach(() => {
    // Reset store to initial state
    usePlanningWizardStore.getState().resetWizard();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const store = getStore();
      
      expect(store.currentStep).toBe(1);
      expect(store.isValid).toEqual({});
      expect(store.wizardData.step1).toEqual({
        title: '',
        subject: '',
        grade: '',
        school: '',
        startDate: '',
        endDate: '',
        duration: 50,
      });
      expect(store.wizardData.step2).toEqual({
        formativeField: '',
        articulatingAxes: [],
      });
    });
  });

  describe('Step Navigation', () => {
    it('can set current step', () => {
      const store = getStore();
      
      store.setCurrentStep(3);
      expect(getStore().currentStep).toBe(3);
    });

    it('can proceed to next step when valid', () => {
      const store = getStore();
      
      // Mark step 1 as valid
      store.setStepValid(1, true);
      
      expect(store.canProceedToStep(2)).toBe(true);
    });

    it('cannot proceed when previous steps are invalid', () => {
      const store = getStore();
      
      // Step 1 is not marked as valid
      expect(store.canProceedToStep(2)).toBe(false);
    });

    it('can proceed to step 3 when steps 1 and 2 are valid', () => {
      const store = getStore();
      
      store.setStepValid(1, true);
      store.setStepValid(2, true);
      
      expect(store.canProceedToStep(3)).toBe(true);
    });
  });

  describe('Step Data Management', () => {
    it('can update step1 data', () => {
      const store = getStore();
      
      const step1Data = {
        title: 'Mi Planeación',
        subject: 'Matemáticas',
        grade: '2° Secundaria',
        school: 'Escuela Primaria',
        startDate: '2024-01-01',
        endDate: '2024-01-31',
        duration: 45,
      };
      
      store.updateStepData('step1', step1Data);
      
      expect(getStore().wizardData.step1).toEqual(step1Data);
    });

    it('can update step2 data', () => {
      const store = getStore();
      
      const step2Data = {
        formativeField: 'saberes-cientifico',
        articulatingAxes: ['inclusion', 'pensamiento-critico'],
      };
      
      store.updateStepData('step2', step2Data);
      
      expect(getStore().wizardData.step2).toEqual(step2Data);
    });

    it('can partially update step data', () => {
      const store = getStore();
      
      // First update
      store.updateStepData('step1', { title: 'Título Inicial' });
      expect(getStore().wizardData.step1?.title).toBe('Título Inicial');
      
      // Partial update should merge with existing data
      store.updateStepData('step1', { subject: 'Matemáticas' });
      
      const step1 = getStore().wizardData.step1;
      expect(step1?.title).toBe('Título Inicial');
      expect(step1?.subject).toBe('Matemáticas');
      expect(step1?.grade).toBe(''); // Should keep initial value
    });

    it('can update step3 data', () => {
      const store = getStore();
      
      const step3Data = {
        pda: 'Proceso de desarrollo del aprendizaje',
        description: 'Descripción detallada',
        objectives: ['Objetivo 1', 'Objetivo 2'],
      };
      
      store.updateStepData('step3', step3Data);
      
      expect(getStore().wizardData.step3).toEqual(step3Data);
    });

    it('can update step4 data', () => {
      const store = getStore();
      
      const step4Data = {
        activities: [
          {
            id: '1',
            name: 'Actividad 1',
            description: 'Descripción de actividad',
            duration: 30,
            type: 'individual',
            materials: ['Material 1', 'Material 2'],
          },
        ],
      };
      
      store.updateStepData('step4', step4Data);
      
      expect(getStore().wizardData.step4).toEqual(step4Data);
    });

    it('can update step5 data', () => {
      const store = getStore();
      
      const step5Data = {
        evaluation: {
          instruments: ['Rúbrica', 'Lista de cotejo'],
          criteria: ['Criterio 1', 'Criterio 2'],
          evidences: ['Evidencia 1', 'Evidencia 2'],
        },
      };
      
      store.updateStepData('step5', step5Data);
      
      expect(getStore().wizardData.step5).toEqual(step5Data);
    });

    it('can update step6 data', () => {
      const store = getStore();
      
      const step6Data = {
        resources: ['Recurso 1', 'Recurso 2'],
        observations: 'Observaciones finales',
      };
      
      store.updateStepData('step6', step6Data);
      
      expect(getStore().wizardData.step6).toEqual(step6Data);
    });
  });

  describe('Validation Management', () => {
    it('can set step as valid', () => {
      const store = getStore();
      
      store.setStepValid(1, true);
      expect(getStore().isValid[1]).toBe(true);
    });

    it('can set step as invalid', () => {
      const store = getStore();
      
      store.setStepValid(1, true);
      store.setStepValid(1, false);
      expect(getStore().isValid[1]).toBe(false);
    });

    it('can set multiple steps as valid', () => {
      const store = getStore();
      
      store.setStepValid(1, true);
      store.setStepValid(2, true);
      store.setStepValid(3, false);
      
      const isValid = getStore().isValid;
      expect(isValid[1]).toBe(true);
      expect(isValid[2]).toBe(true);
      expect(isValid[3]).toBe(false);
    });
  });

  describe('Reset Functionality', () => {
    it('can reset wizard to initial state', () => {
      const store = getStore();
      
      // Make some changes
      store.setCurrentStep(3);
      store.setStepValid(1, true);
      store.updateStepData('step1', { title: 'Test Title' });
      
      // Reset
      store.resetWizard();
      
      const resetStore = getStore();
      expect(resetStore.currentStep).toBe(1);
      expect(resetStore.isValid).toEqual({});
      expect(resetStore.wizardData.step1?.title).toBe('');
    });
  });

  describe('Complex Navigation Scenarios', () => {
    it('handles sequential step validation correctly', () => {
      const store = getStore();
      
      // Can't proceed to step 2 initially
      expect(store.canProceedToStep(2)).toBe(false);
      
      // Mark step 1 as valid
      store.setStepValid(1, true);
      expect(store.canProceedToStep(2)).toBe(true);
      expect(store.canProceedToStep(3)).toBe(false); // Step 2 not valid yet
      
      // Mark step 2 as valid
      store.setStepValid(2, true);
      expect(store.canProceedToStep(3)).toBe(true);
      
      // Can proceed to any step up to 4 now
      expect(store.canProceedToStep(4)).toBe(false); // Step 3 not valid yet
    });

    it('handles non-sequential validation correctly', () => {
      const store = getStore();
      
      // Mark step 2 as valid but not step 1
      store.setStepValid(2, true);
      
      // Still can't proceed to step 2 because step 1 is not valid
      expect(store.canProceedToStep(2)).toBe(false);
      
      // Mark step 1 as valid
      store.setStepValid(1, true);
      
      // Now can proceed to step 2 and 3
      expect(store.canProceedToStep(2)).toBe(true);
      expect(store.canProceedToStep(3)).toBe(true);
    });
  });
});
