import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { PlanningWizard } from '../../../components/planning/PlanningWizard';
import { usePlanningWizardStore } from '../../../stores/planningWizardStore';

// Mock the store
vi.mock('../../../stores/planningWizardStore');

// Mock the step components
vi.mock('../../../components/planning/steps/Step1GeneralInfo', () => ({
  Step1GeneralInfo: () => <div data-testid="step1">Step 1 Content</div>
}));

vi.mock('../../../components/planning/steps/Step2FieldsAndAxes', () => ({
  Step2FieldsAndAxes: () => <div data-testid="step2">Step 2 Content</div>
}));

vi.mock('../../../components/planning/steps/Step3LearningProcess', () => ({
  Step3LearningProcess: () => <div data-testid="step3">Step 3 Content</div>
}));

vi.mock('../../../components/planning/steps/Step4Activities', () => ({
  Step4Activities: () => <div data-testid="step4">Step 4 Content</div>
}));

vi.mock('../../../components/planning/steps/Step5Evaluation', () => ({
  Step5Evaluation: () => <div data-testid="step5">Step 5 Content</div>
}));

vi.mock('../../../components/planning/steps/Step6ReviewAndExport', () => ({
  Step6ReviewAndExport: () => <div data-testid="step6">Step 6 Content</div>
}));

const mockSetCurrentStep = vi.fn();
const mockCanProceedToStep = vi.fn();

const mockStoreData = {
  currentStep: 1,
  setCurrentStep: mockSetCurrentStep,
  isValid: { 1: true, 2: false, 3: false, 4: false, 5: false, 6: false },
  canProceedToStep: mockCanProceedToStep,
  wizardData: {
    step1: {
      title: 'Test Planning',
      subject: 'Matemáticas',
      grade: '2° Secundaria',
      school: 'Test School',
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      duration: 50,
    },
  },
};

describe('PlanningWizard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockCanProceedToStep.mockReturnValue(true);
    (usePlanningWizardStore as any).mockReturnValue(mockStoreData);
  });

  it('renders correctly with step 1', () => {
    render(<PlanningWizard />);
    
    expect(screen.getByText('Paso 1 de 6: Información General')).toBeInTheDocument();
    expect(screen.getByText('17% completado')).toBeInTheDocument();
    expect(screen.getByTestId('step1')).toBeInTheDocument();
  });

  it('shows correct progress for different steps', () => {
    const storeWithStep3 = {
      ...mockStoreData,
      currentStep: 3,
    };
    (usePlanningWizardStore as any).mockReturnValue(storeWithStep3);
    
    render(<PlanningWizard />);
    
    expect(screen.getByText('Paso 3 de 6: Proceso de Desarrollo')).toBeInTheDocument();
    expect(screen.getByText('50% completado')).toBeInTheDocument();
    expect(screen.getByTestId('step3')).toBeInTheDocument();
  });

  it('enables next button when current step is valid', () => {
    render(<PlanningWizard />);
    
    const nextButton = screen.getByRole('button', { name: /siguiente/i });
    expect(nextButton).not.toBeDisabled();
  });

  it('disables next button when current step is invalid', () => {
    const storeWithInvalidStep = {
      ...mockStoreData,
      currentStep: 2,
      isValid: { 1: true, 2: false },
    };
    (usePlanningWizardStore as any).mockReturnValue(storeWithInvalidStep);
    
    render(<PlanningWizard />);
    
    const nextButton = screen.getByRole('button', { name: /siguiente/i });
    expect(nextButton).toBeDisabled();
  });

  it('enables previous button when not on first step', () => {
    const storeWithStep2 = {
      ...mockStoreData,
      currentStep: 2,
    };
    (usePlanningWizardStore as any).mockReturnValue(storeWithStep2);
    
    render(<PlanningWizard />);
    
    const prevButton = screen.getByRole('button', { name: /anterior/i });
    expect(prevButton).not.toBeDisabled();
  });

  it('disables previous button on first step', () => {
    render(<PlanningWizard />);
    
    const prevButton = screen.getByRole('button', { name: /anterior/i });
    expect(prevButton).toBeDisabled();
  });

  it('navigates to next step when next button is clicked', async () => {
    const user = userEvent.setup();
    render(<PlanningWizard />);
    
    const nextButton = screen.getByRole('button', { name: /siguiente/i });
    await user.click(nextButton);
    
    expect(mockSetCurrentStep).toHaveBeenCalledWith(2);
  });

  it('navigates to previous step when previous button is clicked', async () => {
    const user = userEvent.setup();
    const storeWithStep2 = {
      ...mockStoreData,
      currentStep: 2,
    };
    (usePlanningWizardStore as any).mockReturnValue(storeWithStep2);
    
    render(<PlanningWizard />);
    
    const prevButton = screen.getByRole('button', { name: /anterior/i });
    await user.click(prevButton);
    
    expect(mockSetCurrentStep).toHaveBeenCalledWith(1);
  });

  it('shows finish button on last step', () => {
    const storeWithLastStep = {
      ...mockStoreData,
      currentStep: 6,
      isValid: { 1: true, 2: true, 3: true, 4: true, 5: true, 6: true },
    };
    (usePlanningWizardStore as any).mockReturnValue(storeWithLastStep);
    
    render(<PlanningWizard />);
    
    expect(screen.getByRole('button', { name: /finalizar planeación/i })).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /siguiente/i })).not.toBeInTheDocument();
  });

  it('prevents navigation when canProceedToStep returns false', async () => {
    const user = userEvent.setup();
    mockCanProceedToStep.mockReturnValue(false);
    
    render(<PlanningWizard />);
    
    const nextButton = screen.getByRole('button', { name: /siguiente/i });
    await user.click(nextButton);
    
    expect(mockSetCurrentStep).not.toHaveBeenCalled();
  });

  it('shows save draft button', () => {
    render(<PlanningWizard />);
    
    expect(screen.getByRole('button', { name: /guardar borrador/i })).toBeInTheDocument();
  });

  it('displays all step titles correctly', () => {
    const steps = [
      { step: 1, title: 'Información General' },
      { step: 2, title: 'Campo Formativo y Ejes' },
      { step: 3, title: 'Proceso de Desarrollo' },
      { step: 4, title: 'Actividades y Metodología' },
      { step: 5, title: 'Evaluación' },
      { step: 6, title: 'Revisión y Exportación' },
    ];

    steps.forEach(({ step, title }) => {
      const storeWithCurrentStep = {
        ...mockStoreData,
        currentStep: step,
      };
      (usePlanningWizardStore as any).mockReturnValue(storeWithCurrentStep);
      
      const { unmount } = render(<PlanningWizard />);
      expect(screen.getByText(`Paso ${step} de 6: ${title}`)).toBeInTheDocument();
      unmount();
    });
  });
});
