import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Step2FieldsAndAxes } from '../../../components/planning/steps/Step2FieldsAndAxes';
import { usePlanningWizardStore } from '../../../stores/planningWizardStore';

// Mock the store
vi.mock('../../../stores/planningWizardStore');

const mockUpdateStepData = vi.fn();
const mockSetStepValid = vi.fn();

const mockStoreData = {
  wizardData: {
    step1: {
      title: 'Test Planning',
      subject: 'Matemáticas',
      grade: '2° Secundaria',
      school: 'Test School',
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      duration: 50,
    },
    step2: {
      formativeField: '',
      articulatingAxes: [],
    },
  },
  updateStepData: mockUpdateStepData,
  setStepValid: mockSetStepValid,
};

describe('Step2FieldsAndAxes', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (usePlanningWizardStore as any).mockReturnValue(mockStoreData);
  });

  it('renders correctly with initial state', () => {
    render(<Step2FieldsAndAxes />);
    
    expect(screen.getByText('Campo Formativo y Ejes Articuladores')).toBeInTheDocument();
    expect(screen.getByText('Selecciona el marco curricular que guiará tu planeación')).toBeInTheDocument();
    
    // Check debug info shows initial state
    expect(screen.getByText(/Campo: Ninguno/)).toBeInTheDocument();
    expect(screen.getByText(/Ejes: 0/)).toBeInTheDocument();
    expect(screen.getByText(/Válido: No/)).toBeInTheDocument();
  });

  it('displays all formative fields', () => {
    render(<Step2FieldsAndAxes />);
    
    expect(screen.getByText('Lenguajes')).toBeInTheDocument();
    expect(screen.getByText('Saberes y Pensamiento Científico')).toBeInTheDocument();
    expect(screen.getByText('Ética, Naturaleza y Sociedades')).toBeInTheDocument();
    expect(screen.getByText('De lo Humano y lo Comunitario')).toBeInTheDocument();
  });

  it('displays all articulating axes', () => {
    render(<Step2FieldsAndAxes />);
    
    expect(screen.getByText('Inclusión')).toBeInTheDocument();
    expect(screen.getByText('Pensamiento Crítico')).toBeInTheDocument();
    expect(screen.getByText('Interculturalidad Crítica')).toBeInTheDocument();
    expect(screen.getByText('Igualdad de Género')).toBeInTheDocument();
    expect(screen.getByText('Vida Saludable')).toBeInTheDocument();
    expect(screen.getByText('Apropiación de las Culturas a través de la Lectura y la Escritura')).toBeInTheDocument();
    expect(screen.getByText('Artes y Experiencias Estéticas')).toBeInTheDocument();
  });

  it('shows recommendation for current subject', () => {
    render(<Step2FieldsAndAxes />);
    
    expect(screen.getByText(/Recomendado para Matemáticas: Saberes y Pensamiento Científico/)).toBeInTheDocument();
  });

  it('allows selecting a formative field', async () => {
    const user = userEvent.setup();
    render(<Step2FieldsAndAxes />);
    
    const scientificField = screen.getByLabelText('saberes-cientifico');
    await user.click(scientificField);
    
    await waitFor(() => {
      expect(mockUpdateStepData).toHaveBeenCalledWith('step2', expect.objectContaining({
        formativeField: 'saberes-cientifico'
      }));
    });
  });

  it('allows selecting multiple articulating axes', async () => {
    const user = userEvent.setup();
    render(<Step2FieldsAndAxes />);
    
    const inclusionCheckbox = screen.getByLabelText('inclusion');
    const criticalThinkingCheckbox = screen.getByLabelText('pensamiento-critico');
    
    await user.click(inclusionCheckbox);
    await user.click(criticalThinkingCheckbox);
    
    await waitFor(() => {
      expect(mockUpdateStepData).toHaveBeenCalledWith('step2', expect.objectContaining({
        articulatingAxes: expect.arrayContaining(['inclusion', 'pensamiento-critico'])
      }));
    });
  });

  it('allows deselecting articulating axes', async () => {
    const user = userEvent.setup();
    
    // Start with some axes selected
    const storeWithSelectedAxes = {
      ...mockStoreData,
      wizardData: {
        ...mockStoreData.wizardData,
        step2: {
          formativeField: 'saberes-cientifico',
          articulatingAxes: ['inclusion', 'pensamiento-critico'],
        },
      },
    };
    (usePlanningWizardStore as any).mockReturnValue(storeWithSelectedAxes);
    
    render(<Step2FieldsAndAxes />);
    
    const inclusionCheckbox = screen.getByLabelText('inclusion');
    await user.click(inclusionCheckbox);
    
    await waitFor(() => {
      expect(mockUpdateStepData).toHaveBeenCalledWith('step2', expect.objectContaining({
        articulatingAxes: ['pensamiento-critico']
      }));
    });
  });

  it('validates form correctly', async () => {
    const user = userEvent.setup();
    render(<Step2FieldsAndAxes />);
    
    // Initially invalid
    expect(screen.getByText(/Válido: No/)).toBeInTheDocument();
    
    // Select formative field
    const scientificField = screen.getByLabelText('saberes-cientifico');
    await user.click(scientificField);
    
    // Still invalid without axes
    await waitFor(() => {
      expect(screen.getByText(/Campo: saberes-cientifico/)).toBeInTheDocument();
      expect(screen.getByText(/Válido: No/)).toBeInTheDocument();
    });
    
    // Select an axis
    const inclusionCheckbox = screen.getByLabelText('inclusion');
    await user.click(inclusionCheckbox);
    
    // Now should be valid
    await waitFor(() => {
      expect(screen.getByText(/Ejes: 1/)).toBeInTheDocument();
      expect(screen.getByText(/Válido: Sí/)).toBeInTheDocument();
    });
  });

  it('handles clicking on axis container', async () => {
    const user = userEvent.setup();
    render(<Step2FieldsAndAxes />);
    
    // Find the container div for inclusion axis
    const inclusionContainer = screen.getByText('Inclusión').closest('div[class*="p-4"]');
    expect(inclusionContainer).toBeInTheDocument();
    
    await user.click(inclusionContainer!);
    
    await waitFor(() => {
      expect(mockUpdateStepData).toHaveBeenCalledWith('step2', expect.objectContaining({
        articulatingAxes: ['inclusion']
      }));
    });
  });

  it('updates store validation state', async () => {
    const user = userEvent.setup();
    render(<Step2FieldsAndAxes />);
    
    // Select both required fields
    const scientificField = screen.getByLabelText('saberes-cientifico');
    await user.click(scientificField);
    
    const inclusionCheckbox = screen.getByLabelText('inclusion');
    await user.click(inclusionCheckbox);
    
    await waitFor(() => {
      expect(mockSetStepValid).toHaveBeenCalledWith(2, true);
    });
  });
});
