import React, { useEffect } from 'react';
import { Router, Route, Switch, Redirect } from 'wouter';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from './components/ui/toaster';

// Components
import { Navigation } from './components/layout/Navigation';
import { PlanningWizard } from './components/planning/PlanningWizard';

// Pages
import { HomePage } from './pages/HomePage';
import { ChatPage } from './pages/ChatPage';
import { GeneratorPage } from './pages/GeneratorPage';
import { ValidatorPage } from './pages/ValidatorPage';
import { ResourcesPage } from './pages/ResourcesPage';
import { TemplatesPage } from './pages/TemplatesPage';
import { PreviewPage } from './pages/PreviewPage';
import { LoginPage } from './pages/LoginPage';
import { RegisterPage } from './pages/RegisterPage';
import { KnowledgeBasePage } from './pages/KnowledgeBasePage';

// Stores
import { useAuthStore } from './stores/authStore';
import { authApi } from './lib/api';
import { initializePerformance } from './lib/performance-optimizer';
import { initializeAccessibility } from './lib/accessibility';
import { registerServiceWorker } from './lib/performance-optimizer';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
    },
  },
});

// Protected Route Component
function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuthStore();
  
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Redirect to="/login" />;
  }

  return <>{children}</>;
}

// Auth Layout Component
function AuthLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50">
      <div className="container mx-auto px-4 py-8">
        {children}
      </div>
    </div>
  );
}

// Main Layout Component
function MainLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
    </div>
  );
}

function App() {
  const { setUser, setLoading, isAuthenticated } = useAuthStore();

  // Check authentication status on app load
  useEffect(() => {
    const checkAuth = async () => {
      setLoading(true);
      try {
        const result = await authApi.getCurrentUser();
        if (result.data?.user) {
          setUser(result.data.user);
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        // In demo mode, start as logged out
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
    
    // Initialize performance optimizations
    initializePerformance();
    
    // Initialize accessibility features
    initializeAccessibility();
    
    // Register service worker for offline support
    registerServiceWorker();
  }, [setUser, setLoading]);

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <Switch>
          {/* Public Routes */}
          <Route path="/login">
            {isAuthenticated ? (
              <Redirect to="/" />
            ) : (
              <AuthLayout>
                <LoginPage />
              </AuthLayout>
            )}
          </Route>
          
          <Route path="/register">
            {isAuthenticated ? (
              <Redirect to="/" />
            ) : (
              <AuthLayout>
                <RegisterPage />
              </AuthLayout>
            )}
          </Route>

          {/* Protected Routes */}
          <Route path="/">
            <ProtectedRoute>
              <MainLayout>
                <HomePage />
              </MainLayout>
            </ProtectedRoute>
          </Route>

          <Route path="/planning">
            <ProtectedRoute>
              <MainLayout>
                <PlanningWizard />
              </MainLayout>
            </ProtectedRoute>
          </Route>

          <Route path="/chat">
            <ProtectedRoute>
              <MainLayout>
                <ChatPage />
              </MainLayout>
            </ProtectedRoute>
          </Route>

          <Route path="/generator">
            <ProtectedRoute>
              <MainLayout>
                <GeneratorPage />
              </MainLayout>
            </ProtectedRoute>
          </Route>

          <Route path="/validator">
            <ProtectedRoute>
              <MainLayout>
                <ValidatorPage />
              </MainLayout>
            </ProtectedRoute>
          </Route>

          <Route path="/resources">
            <ProtectedRoute>
              <MainLayout>
                <ResourcesPage />
              </MainLayout>
            </ProtectedRoute>
          </Route>

          <Route path="/templates">
            <ProtectedRoute>
              <MainLayout>
                <TemplatesPage />
              </MainLayout>
            </ProtectedRoute>
          </Route>

          <Route path="/knowledge-base">
            <ProtectedRoute>
              <MainLayout>
                <KnowledgeBasePage />
              </MainLayout>
            </ProtectedRoute>
          </Route>

          <Route path="/preview">
            <ProtectedRoute>
              <MainLayout>
                <PreviewPage />
              </MainLayout>
            </ProtectedRoute>
          </Route>

          {/* Default redirect */}
          <Route>
            <Redirect to="/" />
          </Route>
        </Switch>
      </Router>
      
      <Toaster />
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

export default App;