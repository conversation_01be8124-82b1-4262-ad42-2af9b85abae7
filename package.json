{"name": "planeacion-nem", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview", "server": "tsx watch server/index.ts", "db:generate": "drizzle-kit generate:pg", "db:migrate": "tsx server/scripts/migrate.ts", "db:migrate:sep": "tsx server/scripts/migrate-sep-curriculum.ts", "db:studio": "drizzle-kit studio", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:server": "vitest --config vitest.server.config.ts", "test:watch": "vitest --watch", "test:e2e": "playwright test"}, "dependencies": {"@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/express-session": "^1.18.2", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "drizzle-orm": "^0.33.0", "express": "^4.19.2", "express-session": "^1.18.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.445.0", "pg": "^8.12.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-router-dom": "^6.26.2", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "wouter": "^3.7.1", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@playwright/test": "^1.47.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^22.5.5", "@types/pg": "^8.11.8", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/supertest": "^6.0.2", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^1.1.0", "@vitest/ui": "^1.1.0", "autoprefixer": "^10.4.18", "drizzle-kit": "^0.24.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^25.0.0", "postcss": "^8.4.47", "rollup-plugin-visualizer": "^6.0.3", "supertest": "^7.0.0", "tailwindcss": "^3.4.13", "tsx": "^4.19.1", "typescript": "^5.6.2", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^1.0.2", "vitest": "^1.1.0"}}