# 📊 Guía de Configuración - Base de Datos y Sistema de Recursos NEM

## 🗄️ Configuración de Base de Datos

### **1. Instalación de PostgreSQL**
```bash
# macOS con Homebrew
brew install postgresql
brew services start postgresql

# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql

# Windows
# Descargar desde https://www.postgresql.org/download/windows/
```

### **2. Crear Base de Datos**
```bash
# Conectarse a PostgreSQL
psql -U postgres

# Crear base de datos
CREATE DATABASE planeacion_nem;
CREATE USER nem_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE planeacion_nem TO nem_user;
```

### **3. Variables de Entorno**
Crear archivo `.env` en la raíz del proyecto:
```env
# Base de datos
DATABASE_URL=postgresql://nem_user:your_password@localhost:5432/planeacion_nem

# JWT y Sesiones
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here

# Google Gemini AI
GEMINI_API_KEY=your_gemini_api_key_here

# Puerto del servidor
PORT=3001
```

### **4. Ejecutar Migraciones**
```bash
# Instalar dependencias
npm install

# Ejecutar migraciones
npm run db:migrate

# Generar nueva migración (si se agregan cambios)
npm run db:generate
```

## 📚 Sistema de Recursos NEM - Documentación Técnica

### **Tablas de Base de Datos**

#### **nem_resources** - Recursos educativos
```sql
- id (UUID): Identificador único
- title (VARCHAR): Título del recurso
- type (VARCHAR): Tipo (pda, activity, project, rubric, document, video, image)
- category (VARCHAR): Campo formativo
- grade (VARCHAR): Grado escolar
- subject (VARCHAR): Materia
- content (JSONB): Contenido estructurado
- tags (JSONB): Array de etiquetas
- isOfficial (BOOLEAN): ¿Es oficial SEP?
- downloads (INTEGER): Contador de descargas
- rating (INTEGER): Calificación promedio
- createdAt (TIMESTAMP): Fecha de creación
```

#### **resource_ratings** - Calificaciones
```sql
- id (UUID): Identificador único
- userId (UUID): Usuario que calificó
- resourceId (UUID): Recurso calificado
- rating (INTEGER): Calificación 1-5
- createdAt (TIMESTAMP): Fecha de calificación
```

#### **resource_favorites** - Favoritos
```sql
- id (UUID): Identificador único
- userId (UUID): Usuario que marcó como favorito
- resourceId (UUID): Recurso favorito
- createdAt (TIMESTAMP): Fecha de favorito
```

#### **resource_downloads** - Descargas
```sql
- id (UUID): Identificador único
- userId (UUID): Usuario que descargó
- resourceId (UUID): Recurso descargado
- downloadedAt (TIMESTAMP): Fecha de descarga
- ipAddress (VARCHAR): IP del usuario
```

### **API Endpoints - Sistema de Recursos**

#### **GET /api/resources**
Obtener lista de recursos con filtros opcionales:
```bash
# Parámetros de consulta:
?type=pda&category=Lenguajes&grade=1°%20Secundaria&search=oralidad
```

#### **GET /api/resources/:id**
Obtener detalle de un recurso específico.

#### **POST /api/resources/:id/rate**
Calificar un recurso:
```json
{
  "rating": 5
}
```

#### **POST /api/resources/:id/favorite**
Marcar/desmarcar como favorito.

#### **POST /api/resources/:id/download**
Registrar una descarga.

#### **GET /api/resources/favorites/me**
Obtener favoritos del usuario autenticado.

#### **GET /api/resources/featured/popular**
Obtener recursos más populares.

#### **POST /api/resources/upload**
Subir nuevo recurso:
```json
{
  "title": "Nuevo Recurso",
  "type": "activity",
  "category": "Lenguajes",
  "grade": "1° Secundaria",
  "subject": "Español",
  "content": { ... },
  "tags": ["tag1", "tag2"]
}
```

## 🚀 Instalación Rápida

### **1. Clonar y configurar**
```bash
git clone [repository-url]
cd planeacion-nem
npm install
```

### **2. Configurar base de datos**
```bash
# Copiar archivo de ejemplo
cp .env.example .env

# Editar .env con tus credenciales
# DATABASE_URL=postgresql://...
```

### **3. Ejecutar migraciones**
```bash
npm run db:migrate
```

### **4. Iniciar servidor**
```bash
# Desarrollo
npm run dev

# Producción
npm run build
npm start
```

## 📋 Datos de Prueba

### **Recursos de muestra incluidos:**
1. **PDA: Participa en intercambios orales** - Oficial SEP
2. **Actividad: Debate sobre Cambio Climático** - Comunidad
3. **Proyecto: Huerto Escolar Sustentable** - Oficial SEP
4. **Rúbrica: Expresión Oral** - Oficial SEP
5. **Documento: Marco Curricular NEM 2024** - Oficial SEP

## 🔧 Comandos Útiles

```bash
# Desarrollo
npm run dev              # Iniciar servidor + cliente
npm run server:dev       # Solo servidor
npm run client:dev       # Solo cliente

# Base de datos
npm run db:migrate       # Ejecutar migraciones
npm run db:generate      # Generar nueva migración
npm run db:studio        # Drizzle Studio UI

# Testing
npm run test            # Ejecutar pruebas
npm run test:watch      # Pruebas en modo watch
npm run lint            # Linting
npm run build           # Build de producción
```

## 📞 Soporte

Para problemas de configuración:
1. Verificar que PostgreSQL esté ejecutándose
2. Confirmar credenciales en `.env`
3. Ejecutar `npm run db:migrate` después de cambios
4. Revisar logs en consola para errores específicos

**Contacto:** [<EMAIL>]