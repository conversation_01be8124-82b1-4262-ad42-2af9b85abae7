# Guía de Solución de Problemas

## Errores Comunes y Soluciones

### 1. Error: "Failed to fetch" / "net::ERR_FAILED"

**Causa**: El frontend no puede conectarse al backend
**Solución**:

1. **Verificar puertos**:
   - Backend debe estar en `http://localhost:3001`
   - Frontend debe estar en `http://localhost:5173`

2. **Reiniciar servicios**:
   ```bash
   # Terminal 1 - Backend
   npm run server

   # Terminal 2 - Frontend
   npm run dev
   ```

3. **Verificar conexión**:
   - Abre `http://localhost:3001/health` en tu navegador
   - Debe mostrar: `{"status":"ok","timestamp":"..."}`

### 2. Error: Service Worker "Failed to fetch"

**Solución**:
1. **Limpiar cache del navegador**:
   - Chrome: F12 → Application → Service Workers → Unregister
   - Luego: Application → Clear storage → Clear site data

2. **Recargar sin cache**:
   - Ctrl + Shift + R (Windows/Linux)
   - Cmd + Shift + R (Mac)

### 3. Error: AxiosError / Network Error

**Verificar paso a paso**:

1. **Backend está corriendo**:
   ```bash
   # En otra terminal
   curl http://localhost:3001/health
   ```

2. **Puerto correcto**:
   - Backend: Puerto 3001 (configurado en server/index.ts)
   - Frontend: Puerto 5173 (configurado en vite.config.ts)

3. **Variables de entorno**:
   ```bash
   # Verificar .env
   cat .env
   # Debe tener:
   # SESSION_SECRET=...
   # CLIENT_URL=http://localhost:5173
   # DATABASE_URL=...
   # GEMINI_API_KEY=...
   ```

### 4. Error de CORS

**Solución**:
- Ya está configurado CORS en server/index.ts
- Asegúrate de que CLIENT_URL en .env sea `http://localhost:5173`

### 5. Database Connection Error

**Solución**:
1. **Verificar Docker**:
   ```bash
   docker-compose ps
   # Debe mostrar postgres corriendo en puerto 5433
   ```

2. **Verificar conexión**:
   ```bash
   # Probar conexión a PostgreSQL
   psql postgresql://postgres:postgres@localhost:5433/planeacion_nem
   ```

## Comandos de Diagnóstico

### Verificar servicios
```bash
# Verificar qué está corriendo
lsof -i :3001  # Backend
lsof -i :5173  # Frontend
lsof -i :5433  # PostgreSQL
```

### Logs completos
```bash
# Backend logs
npm run server

# Frontend logs  
npm run dev

# Database logs (si usas Docker)
docker-compose logs postgres
```

## Configuración Correcta

### Backend (server/index.ts)
- Puerto: 3001
- CORS: Permitido desde http://localhost:5173
- Rutas: /api/*

### Frontend (vite.config.ts)
- Puerto: 5173
- Proxy: /api → http://localhost:3001

### Service Worker (public/sw.js)
- Actualizado para manejar errores de red
- Ignora peticiones cross-origin
- Manejo de fallos gracefully

## Pasos para Resetear Todo

1. **Detener todos los servicios**:
   ```bash
   # En las terminales, presiona Ctrl+C
   ```

2. **Limpiar cache**:
   ```bash
   # Limpiar node_modules si es necesario
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **Reiniciar Docker** (si usas PostgreSQL):
   ```bash
   docker-compose down
   docker-compose up -d
   ```

4. **Reiniciar servicios**:
   ```bash
   # Terminal 1
   npm run server

   # Terminal 2
   npm run dev
   ```

## Verificación Final

Después de seguir estos pasos:

1. Backend: `http://localhost:3001/health` ✅
2. Frontend: `http://localhost:5173` ✅
3. Database: `http://localhost:3001/api/auth/me` (debe dar error 401 sin login) ✅
4. Login: Usa credenciales demo: `<EMAIL>` / `demo123` ✅