# Inicio Rápido - Planeación NEM

## 🚀 Credenciales Demo

**Usuario Demo:**
- **Email**: `<EMAIL>`
- **Contraseña**: `demo123`

## 📋 Pasos para Iniciar

### 1. Verificar que todo esté corriendo
```bash
# Backend debe estar en: http://localhost:3001
# Frontend debe estar en: http://localhost:5173
```

### 2. Probar credenciales
- Abre `http://localhost:5173`
- Usa las credenciales demo arriba
- ¡Listo para usar!

## 🔧 Estado Actual

✅ **Backend**: Corriendo en puerto 3001 sin errores 500
✅ **Frontend**: Corriendo en puerto 5173
✅ **Base de datos**: PostgreSQL con tablas migradas
✅ **Autenticación**: Funcionando correctamente
✅ **Gemini AI**: Configurado y listo para usar

## 🎯 Errores Resueltos

- ✅ **500 Internal Server Error**: Resuelto con configuración de sesión
- ✅ **401 Unauthorized**: Comportamiento normal cuando no hay sesión
- ✅ **Failed to fetch**: Resuelto con puertos correctos
- ✅ **Base de datos**: Configurada con credenciales correctas

## 📝 Notas Importantes

- **Error 401**: Es comportamiento normal cuando no estás logueado
- **Credenciales demo**: Ya están creadas y funcionando
- **Gemini API**: Necesitas agregar tu API key en `.env` para usar IA