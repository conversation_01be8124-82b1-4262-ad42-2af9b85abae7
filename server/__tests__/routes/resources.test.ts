import { describe, it, expect } from 'vitest';
import request from 'supertest';
import express from 'express';

// Create a simple test app
const app = express();
app.use(express.json());

// Mock route for testing
app.get('/api/resources', (_req, res) => {
  res.json({
    resources: [
      {
        id: 1,
        title: 'Test Resource',
        type: 'pdf',
        category: 'planning',
      },
    ],
  });
});

app.get('/api/resources/:id', (req, res) => {
  const { id } = req.params;
  if (id === '1') {
    res.json({
      resource: {
        id: 1,
        title: 'Test Resource',
        type: 'pdf',
      },
    });
  } else {
    res.status(404).json({ error: 'Resource not found' });
  }
});

describe('Resources API', () => {
  it('should get all resources', async () => {
    const response = await request(app)
      .get('/api/resources')
      .expect(200);

    expect(response.body).toHaveProperty('resources');
    expect(Array.isArray(response.body.resources)).toBe(true);
    expect(response.body.resources).toHaveLength(1);
  });

  it('should get resource by id', async () => {
    const response = await request(app)
      .get('/api/resources/1')
      .expect(200);

    expect(response.body).toHaveProperty('resource');
    expect(response.body.resource).toHaveProperty('id', 1);
  });

  it('should return 404 for non-existent resource', async () => {
    const response = await request(app)
      .get('/api/resources/999')
      .expect(404);

    expect(response.body).toHaveProperty('error', 'Resource not found');
  });
});