import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { 
  sepCurriculumFrameworks, 
  sepFormativeFields, 
  sepArticulatingAxes, 
  sepLearningPurposes,
  sepProjects,
  sepNormativeDocuments,
  sepOfficialTemplates,
  sepAssessmentInstruments
} from '../db/schema-sep-curriculum.js';
import { config } from 'dotenv';

config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

const db = drizzle(pool);

// Official SEP Curriculum Data - Programa Sintético Fase 6
const curriculumData = {
  framework: {
    id: 'ps-fase-6-2022',
    name: 'Programa Sintético Fase 6',
    version: '2022',
    phase: 'Fase 6',
    description: 'Programa Sintético de la Nueva Escuela Mexicana para Educación Primaria',
    isActive: true
  },
  formativeFields: [
    {
      id: 'lyc',
      frameworkId: 'ps-fase-6-2022',
      name: 'Lenguaje y Comunicación',
      code: 'LYC',
      description: 'Desarrollo de habilidades lingüísticas y comunicativas',
      gradeRange: '1°-6°',
      order: 1
    },
    {
      id: 'pm',
      frameworkId: 'ps-fase-6-2022',
      name: 'Pensamiento Matemático',
      code: 'PM',
      description: 'Desarrollo del pensamiento lógico-matemático',
      gradeRange: '1°-6°',
      order: 2
    },
    {
      id: 'pe',
      frameworkId: 'ps-fase-6-2022',
      name: 'Exploración y Comprensión del Mundo Natural y Social',
      code: 'PE',
      description: 'Exploración científica y social del entorno',
      gradeRange: '1°-6°',
      order: 3
    },
    {
      id: 'iyc',
      frameworkId: 'ps-fase-6-2022',
      name: 'Iniciativa y Desarrollo Personal y para la Convivencia',
      code: 'IYC',
      description: 'Desarrollo personal y social',
      gradeRange: '1°-6°',
      order: 4
    }
  ],
  articulatingAxes: [
    // LYC Axes
    { id: 'lyc-e1', fieldId: 'lyc', name: 'Explora y comprende el entorno', code: 'E1', grade: '1°', order: 1 },
    { id: 'lyc-e2', fieldId: 'lyc', name: 'Resuelve problemas', code: 'E2', grade: '1°', order: 2 },
    { id: 'lyc-e3', fieldId: 'lyc', name: 'Argumenta y comunica', code: 'E3', grade: '1°', order: 3 },
    
    // PM Axes
    { id: 'pm-e1', fieldId: 'pm', name: 'Explora y comprende el entorno', code: 'E1', grade: '1°', order: 1 },
    { id: 'pm-e2', fieldId: 'pm', name: 'Resuelve problemas', code: 'E2', grade: '1°', order: 2 },
    { id: 'pm-e3', fieldId: 'pm', name: 'Argumenta y comunica', code: 'E3', grade: '1°', order: 3 },
    
    // Continue for other grades...
    { id: 'lyc-e1-2', fieldId: 'lyc', name: 'Explora y comprende el entorno', code: 'E1', grade: '2°', order: 1 },
    { id: 'lyc-e2-2', fieldId: 'lyc', name: 'Resuelve problemas', code: 'E2', grade: '2°', order: 2 },
    { id: 'lyc-e3-2', fieldId: 'lyc', name: 'Argumenta y comunica', code: 'E3', grade: '2°', order: 3 }
  ],
  learningPurposes: [
    // 1° LYC
    {
      id: '1lyc1.1',
      axisId: 'lyc-e1',
      fieldId: 'lyc',
      grade: '1°',
      subject: 'Español',
      purpose: 'Explora textos orales y escritos para comprender su propósito comunicativo y participar en situaciones significativas de su vida cotidiana.',
      code: '1LYC1.1',
      expectedLearning: [
        'Identifica el propósito comunicativo de textos orales y escritos',
        'Reconoce elementos de la comunicación oral y escrita',
        'Participa activamente en situaciones comunicativas'
      ],
      indicators: [
        'Identifica el propósito de diferentes textos',
        'Participa en conversaciones y lecturas compartidas',
        'Expresa ideas de forma oral y escrita'
      ]
    },
    {
      id: '1lyc2.1',
      axisId: 'lyc-e2',
      fieldId: 'lyc',
      grade: '1°',
      subject: 'Español',
      purpose: 'Utiliza el conocimiento de la lengua escrita para producir textos con propósitos comunicativos.',
      code: '1LYC2.1',
      expectedLearning: [
        'Utiliza el conocimiento de la lengua escrita',
        'Produce textos con propósitos comunicativos',
        'Aplica convenciones de escritura'
      ],
      indicators: [
        'Escribe palabras y frases simples',
        'Utiliza letras mayúsculas y minúsculas',
        'Respeta la puntuación básica'
      ]
    },
    // 1° PM
    {
      id: '1pm1.1',
      axisId: 'pm-e1',
      fieldId: 'pm',
      grade: '1°',
      subject: 'Matemáticas',
      purpose: 'Explora y comprende el entorno a través de la observación, la manipulación y la representación de cantidades y formas.',
      code: '1PM1.1',
      expectedLearning: [
        'Explora y comprende cantidades y formas',
        'Representa cantidades y formas',
        'Resuelve problemas matemáticos simples'
      ],
      indicators: [
        'Cuenta objetos hasta 100',
        'Identifica formas geométricas básicas',
        'Resuelve problemas de suma y resta'
      ]
    }
  ],
  projects: [
    {
      id: 'proyecto-comunidad-1',
      name: 'Mi Comunidad',
      grade: '1°',
      subject: 'Exploración',
      type: 'proyecto_aula',
      duration: '4 semanas',
      description: 'Proyecto para conocer y valorar la comunidad escolar',
      objectives: [
        'Conocer los miembros de la comunidad escolar',
        'Identificar las funciones de cada miembro',
        'Valorar la importancia del trabajo en equipo'
      ],
      phases: [
        { name: 'Exploración', duration: '1 semana', activities: ['Observación de la escuela', 'Entrevistas'] },
        { name: 'Investigación', duration: '1 semana', activities: ['Recolección de información', 'Visitas guiadas'] },
        { name: 'Producción', duration: '1 semana', activities: ['Creación de murales', 'Presentaciones'] },
        { name: 'Socialización', duration: '1 semana', activities: ['Exposición a la comunidad', 'Reflexión'] }
      ],
      resources: ['Cámaras', 'Materiales de arte', 'Libros sobre comunidad'],
      evaluationCriteria: ['Participación activa', 'Trabajo colaborativo', 'Producto final']
    }
  ],
  normativeDocuments: [
    {
      id: 'acuerdo-11-06-18',
      title: 'Acuerdo número 11/06/18',
      documentType: 'acuerdo',
      number: '11/06/18',
      date: new Date('2018-06-11'),
      description: 'Plan de Estudios 2017 para la Educación Obligatoraria',
      category: 'curriculum',
      content: {
        sections: [
          'Fundamentación',
          'Perfil de egreso',
          'Campos formativos',
          'Enfoques pedagógicos'
        ]
      },
      url: 'https://www.sep.gob.mx/work/models/sep1/Resource/5589c4c5-dc2e-4a84-b2c6-4c5ad9f2e3ac/Acuerdo_11_06_18.pdf'
    },
    {
      id: 'acuerdo-12-06-18',
      title: 'Acuerdo número 12/06/18',
      documentType: 'acuerdo',
      number: '12/06/18',
      date: new Date('2018-06-12'),
      description: 'Programas de estudio para la educación primaria',
      category: 'curriculum',
      content: {
        sections: [
          'Programa de Español',
          'Programa de Matemáticas',
          'Programa de Exploración',
          'Programa de Formación Cívica y Ética'
        ]
      },
      url: 'https://www.sep.gob.mx/work/models/sep1/Resource/5589c4c5-dc2e-4a84-b2c6-4c5ad9f2e3ac/Acuerdo_12_06_18.pdf'
    }
  ],
  officialTemplates: [
    {
      id: 'plantilla-nem-1',
      name: 'Plantilla de Planeación NEM',
      type: 'planeacion',
      description: 'Plantilla oficial para planeaciones didácticas alineadas con la NEM',
      structure: {
        sections: [
          'Datos generales',
          'Propósito de aprendizaje',
          'Eje articulador',
          'Actividades de aprendizaje',
          'Evaluación',
          'Recursos',
          'Reflexión'
        ]
      },
      guidelines: [
        'Completa todos los campos obligatorios',
        'Alinea con los propósitos de aprendizaje',
        'Incluye actividades socioconstructivistas',
        'Considera la diversidad del aula'
      ]
    },
    {
      id: 'plantilla-actividad-1',
      name: 'Plantilla de Actividad Socioconstructivista',
      type: 'actividad',
      description: 'Plantilla para diseñar actividades alineadas con el enfoque socioconstructivista',
      structure: {
        sections: [
          'Título de la actividad',
          'Propósito',
          'Materiales',
          'Desarrollo de la actividad',
          'Evaluación',
          'Reflexión'
        ]
      }
    }
  ],
  assessmentInstruments: [
    {
      id: 'rubrica-formativa-1',
      name: 'Rúbrica de Evaluación Formativa',
      type: 'rubrica',
      description: 'Rúbrica para evaluar el proceso de aprendizaje de los estudiantes',
      structure: {
        dimensions: [
          'Participación activa',
          'Trabajo colaborativo',
          'Resolución de problemas',
          'Comunicación de ideas'
        ],
        levels: [
          { name: 'Excelente', description: 'Supera las expectativas' },
          { name: 'Bien', description: 'Cumple las expectativas' },
          { name: 'Suficiente', description: 'Cumple parcialmente' },
          { name: 'Necesita mejorar', description: 'No cumple las expectativas' }
        ]
      }
    },
    {
      id: 'lista-cotejo-1',
      name: 'Lista de Cotejo para Proyectos',
      type: 'lista_cotejo',
      description: 'Instrumento para verificar el cumplimiento de criterios en proyectos',
      structure: {
        criteria: [
          'Definición clara del problema',
          'Planificación del proyecto',
          'Ejecución de actividades',
          'Producto final',
          'Presentación y socialización'
        ]
      }
    }
  ]
};

async function migrateSEPData() {
  try {
    console.log('🚀 Iniciando migración de datos SEP...');

    // Insert framework
    await db.insert(sepCurriculumFrameworks).values(curriculumData.framework);
    console.log('✅ Marco curricular insertado');

    // Insert formative fields
    await db.insert(sepFormativeFields).values(curriculumData.formativeFields);
    console.log('✅ Campos formativos insertados');

    // Insert articulating axes
    await db.insert(sepArticulatingAxes).values(curriculumData.articulatingAxes);
    console.log('✅ Ejes articuladores insertados');

    // Insert learning purposes (PDAs)
    await db.insert(sepLearningPurposes).values(curriculumData.learningPurposes);
    console.log('✅ Propósitos de aprendizaje insertados');

    // Insert projects
    await db.insert(sepProjects).values(curriculumData.projects);
    console.log('✅ Proyectos insertados');

    // Insert normative documents
    await db.insert(sepNormativeDocuments).values(curriculumData.normativeDocuments);
    console.log('✅ Documentos normativos insertados');

    // Insert official templates
    await db.insert(sepOfficialTemplates).values(curriculumData.officialTemplates);
    console.log('✅ Plantillas oficiales insertadas');

    // Insert assessment instruments
    await db.insert(sepAssessmentInstruments).values(curriculumData.assessmentInstruments);
    console.log('✅ Instrumentos de evaluación insertados');

    console.log('🎉 Migración completada exitosamente');
  } catch (error) {
    console.error('❌ Error en la migración:', error);
  } finally {
    await pool.end();
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  migrateSEPData();
}

export { migrateSEPData };