import { db } from '../db/index';
import { nemResources } from '../db/schema';

const sampleResources = [
  {
    title: 'Planeación de Lenguaje y Comunicación - Primer Grado',
    type: 'planeacion',
    category: 'Lenguaje y Comunicación',
    grade: '1°',
    subject: 'Lenguaje y Comunicación',
    content: {
      objetivo: 'Desarrollar habilidades de lectura y escritura',
      actividades: [
        'Lectura de cuentos',
        'Escritura de oraciones simples',
        'Juegos de palabras'
      ],
      duracion: '4 semanas',
      recursos: ['Libros de cuentos', 'Lápices', 'Papel']
    },
    tags: ['lectura', 'escritura', 'primer grado', 'lenguaje'],
    isOfficial: true,
    downloads: 125,
    rating: 4.8
  },
  {
    title: 'Actividad de Matemáticas - Segundo Grado',
    type: 'actividad',
    category: 'Pensamiento Matemático',
    grade: '2°',
    subject: 'Matemáticas',
    content: {
      objetivo: 'Comprender la suma y resta con llevadas',
      actividades: [
        'Resolución de problemas',
        'Uso de material concreto',
        'Juegos matemáticos'
      ],
      duracion: '2 semanas',
      recursos: ['Fichas', 'Abaco', 'Problemas impresos']
    },
    tags: ['suma', 'resta', 'segundo grado', 'matemáticas'],
    isOfficial: true,
    downloads: 89,
    rating: 4.6
  },
  {
    title: 'Proyecto Comunitario - Medio Ambiente',
    type: 'proyecto',
    category: 'Exploración y Comprensión del Mundo Natural y Social',
    grade: '3°',
    subject: 'Ciencias Naturales',
    content: {
      objetivo: 'Cuidar el medio ambiente en la comunidad',
      actividades: [
        'Recolección de basura',
        'Plantación de árboles',
        'Campaña de concientización'
      ],
      duracion: '3 semanas',
      recursos: ['Guantes', 'Bolsas', 'Plantas']
    },
    tags: ['medio ambiente', 'comunidad', 'proyecto', 'ciencias'],
    isOfficial: true,
    downloads: 67,
    rating: 4.7
  },
  {
    title: 'Rúbrica de Evaluación - Expresión Artística',
    type: 'rubrica',
    category: 'Expresión y Apreciación Artísticas',
    grade: '1°-3°',
    subject: 'Artes',
    content: {
      criterios: [
        'Participación activa',
        'Creatividad',
        'Uso de materiales',
        'Trabajo en equipo'
      ],
      niveles: ['Excelente', 'Bueno', 'Suficiente', 'Necesita mejorar'],
      descripcion: 'Evaluación del desempeño en actividades artísticas'
    },
    tags: ['evaluación', 'artes', 'rúbrica', 'primaria'],
    isOfficial: true,
    downloads: 156,
    rating: 4.9
  },
  {
    title: 'Planeación de Educación Cívica - Tercer Grado',
    type: 'planeacion',
    category: 'Educación Cívica y Ética',
    grade: '3°',
    subject: 'Formación Cívica y Ética',
    content: {
      objetivo: 'Desarrollar valores cívicos y responsabilidad social',
      actividades: [
        'Dramatizaciones',
        'Debates',
        'Visitas comunitarias'
      ],
      duracion: '5 semanas',
      recursos: ['Material audiovisual', 'Visitas guiadas']
    },
    tags: ['cívica', 'ética', 'valores', 'tercer grado'],
    isOfficial: true,
    downloads: 78,
    rating: 4.5
  }
];

async function seedSEPResources() {
  console.log('🌱 Iniciando seed de recursos SEP...');
  
  try {
    for (const resource of sampleResources) {
      await db.insert(nemResources).values({
        ...resource,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log(`✅ Agregado: ${resource.title}`);
    }
    
    console.log('🎉 Seed completado exitosamente');
  } catch (error) {
    console.error('❌ Error en seed:', error);
  }
}

// Ejecutar si se llama directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  seedSEPResources();
}

export { seedSEPResources };