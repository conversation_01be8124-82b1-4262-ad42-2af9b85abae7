import bcrypt from 'bcryptjs';
import { db } from '../db/index.js';
import { users } from '../db/schema.js';
import { eq } from 'drizzle-orm';

async function seedDemoUser() {
  try {
    // Check if demo user already exists
    const existingUser = await db.select().from(users).where(eq(users.email, '<EMAIL>'));
    
    if (existingUser.length === 0) {
      // Create demo user
      const hashedPassword = await bcrypt.hash('demo123', 10);
      
      await db.insert(users).values({
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Profesor',
        lastName: 'Demo',
        school: 'Escuela Demo',
        grade: '2° Secundaria',
        subject: 'Matemáticas',
        zone: 'Zona Demo',
        sector: 'Sector Demo'
      });
      
      console.log('✅ Usuario demo creado exitosamente');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Contraseña: demo123');
    } else {
      console.log('ℹ️ Usuario demo ya existe');
    }
  } catch (error) {
    console.error('❌ Error al crear usuario demo:', error);
  }
}

seedDemoUser();