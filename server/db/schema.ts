import { pgTable, text, timestamp, uuid, jsonb, boolean, integer, varchar } from 'drizzle-orm/pg-core';

// Users table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().default('gen_random_uuid()'),
  email: varchar('email', { length: 255 }).unique().notNull(),
  password: text('password').notNull(),
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  school: varchar('school', { length: 200 }),
  grade: varchar('grade', { length: 50 }),
  subject: varchar('subject', { length: 100 }),
  zone: varchar('zone', { length: 100 }),
  sector: varchar('sector', { length: 100 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Plannings table - Core lesson plans
export const plannings = pgTable('plannings', {
  id: uuid('id').primaryKey().default('gen_random_uuid()'),
  userId: uuid('user_id').references(() => users.id).notNull(),
  title: varchar('title', { length: 300 }).notNull(),
  subject: varchar('subject', { length: 100 }).notNull(),
  grade: varchar('grade', { length: 50 }).notNull(),
  formativeField: varchar('formative_field', { length: 100 }).notNull(), // Campo Formativo
  articulatingAxes: jsonb('articulating_axes').notNull(), // Ejes Articuladores
  learningProcess: jsonb('learning_process').notNull(), // PDA - Proceso de Desarrollo de Aprendizaje
  activities: jsonb('activities').notNull(),
  evaluation: jsonb('evaluation').notNull(),
  resources: jsonb('resources'),
  startDate: timestamp('start_date').notNull(),
  endDate: timestamp('end_date').notNull(),
  status: varchar('status', { length: 50 }).default('draft').notNull(), // draft, in_progress, completed
  coherenceScore: integer('coherence_score'), // AI-generated coherence score
  isPublic: boolean('is_public').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Chat messages for AI assistant
export const chatMessages = pgTable('chat_messages', {
  id: uuid('id').primaryKey().default('gen_random_uuid()'),
  userId: uuid('user_id').references(() => users.id).notNull(),
  planningId: uuid('planning_id').references(() => plannings.id),
  role: varchar('role', { length: 20 }).notNull(), // user, assistant, system
  content: text('content').notNull(),
  context: jsonb('context'), // Planning step, formative field, etc.
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// NEM Resources - Official SEP documents and resources
export const nemResources = pgTable('nem_resources', {
  id: uuid('id').primaryKey().default('gen_random_uuid()'),
  title: varchar('title', { length: 300 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(), // pda, activity, project, rubric, etc.
  category: varchar('category', { length: 100 }).notNull(), // Campo Formativo
  grade: varchar('grade', { length: 50 }),
  subject: varchar('subject', { length: 100 }),
  content: jsonb('content').notNull(),
  tags: jsonb('tags'),
  isOfficial: boolean('is_official').default(false).notNull(), // SEP official content
  downloads: integer('downloads').default(0).notNull(),
  rating: integer('rating').default(0),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Resource ratings table
export const resourceRatings = pgTable('resource_ratings', {
  id: uuid('id').primaryKey().default('gen_random_uuid()'),
  userId: uuid('user_id').references(() => users.id).notNull(),
  resourceId: uuid('resource_id').references(() => nemResources.id).notNull(),
  rating: integer('rating').notNull(), // 1-5 stars
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Resource favorites table
export const resourceFavorites = pgTable('resource_favorites', {
  id: uuid('id').primaryKey().default('gen_random_uuid()'),
  userId: uuid('user_id').references(() => users.id).notNull(),
  resourceId: uuid('resource_id').references(() => nemResources.id).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Resource downloads tracking
export const resourceDownloads = pgTable('resource_downloads', {
  id: uuid('id').primaryKey().default('gen_random_uuid()'),
  userId: uuid('user_id').references(() => users.id),
  resourceId: uuid('resource_id').references(() => nemResources.id).notNull(),
  downloadedAt: timestamp('downloaded_at').defaultNow().notNull(),
  ipAddress: varchar('ip_address', { length: 45 }),
});

// Templates for planning
export const templates = pgTable('templates', {
  id: uuid('id').primaryKey().default('gen_random_uuid()'),
  userId: uuid('user_id').references(() => users.id),
  name: varchar('name', { length: 200 }).notNull(),
  description: text('description'),
  type: varchar('type', { length: 50 }).notNull(), // planning, activity, project, evaluation
  formativeField: varchar('formative_field', { length: 100 }),
  grade: varchar('grade', { length: 50 }),
  subject: varchar('subject', { length: 100 }),
  template: jsonb('template').notNull(),
  isPublic: boolean('is_public').default(false).notNull(),
  usage: integer('usage').default(0).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// User sessions
export const sessions = pgTable('sessions', {
  sid: varchar('sid', { length: 36 }).primaryKey(),
  userId: uuid('user_id').references(() => users.id),
  expires: timestamp('expires').notNull(),
  data: text('data'),
});

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Planning = typeof plannings.$inferSelect;
export type NewPlanning = typeof plannings.$inferInsert;
export type ChatMessage = typeof chatMessages.$inferSelect;
export type NewChatMessage = typeof chatMessages.$inferInsert;
export type NemResource = typeof nemResources.$inferSelect;
export type NewNemResource = typeof nemResources.$inferInsert;
export type Template = typeof templates.$inferSelect;
export type NewTemplate = typeof templates.$inferInsert;
export type ResourceRating = typeof resourceRatings.$inferSelect;
export type NewResourceRating = typeof resourceRatings.$inferInsert;
export type ResourceFavorite = typeof resourceFavorites.$inferSelect;
export type NewResourceFavorite = typeof resourceFavorites.$inferInsert;
export type ResourceDownload = typeof resourceDownloads.$inferSelect;
export type NewResourceDownload = typeof resourceDownloads.$inferInsert;