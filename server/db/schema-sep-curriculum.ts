import { pgTable, text, integer, boolean, timestamp, jsonb, primaryKey } from 'drizzle-orm/pg-core';

// Official SEP Curriculum Framework - Nueva Escuela Mexicana
export const sepCurriculumFrameworks = pgTable('sep_curriculum_frameworks', {
  id: text('id').primaryKey(),
  name: text('name').notNull(), // "Programa Sintético Fase 6", "Plan de Estudios 2022"
  version: text('version').notNull(), // "2022", "2023", etc.
  phase: text('phase').notNull(), // "Fase 6", "Fase 5", etc.
  description: text('description'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Official Formative Fields (Campos Formativos)
export const sepFormativeFields = pgTable('sep_formative_fields', {
  id: text('id').primaryKey(),
  frameworkId: text('framework_id').references(() => sepCurriculumFrameworks.id),
  name: text('name').notNull(), // "Lenguaje y Comunicación", "Pensamiento Matemático"
  code: text('code').notNull(), // "LYC", "PM"
  description: text('description'),
  gradeRange: text('grade_range'), // "1°-3°", "4°-6°"
  order: integer('order'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Articulating Axes (Ejes Articuladores)
export const sepArticulatingAxes = pgTable('sep_articulating_axes', {
  id: text('id').primaryKey(),
  fieldId: text('field_id').references(() => sepFormativeFields.id),
  name: text('name').notNull(), // "Explora y comprende el entorno", "Resuelve problemas"
  code: text('code').notNull(), // "E1", "E2"
  description: text('description'),
  grade: text('grade'), // "1°", "2°", etc.
  order: integer('order'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Official PDAs (Propósitos de Aprendizaje)
export const sepLearningPurposes = pgTable('sep_learning_purposes', {
  id: text('id').primaryKey(),
  axisId: text('axis_id').references(() => sepArticulatingAxes.id),
  fieldId: text('field_id').references(() => sepFormativeFields.id),
  grade: text('grade').notNull(), // "1°", "2°", etc.
  subject: text('subject').notNull(), // "Español", "Matemáticas"
  purpose: text('purpose').notNull(), // Full PDA text
  code: text('code').notNull(), // "1LYC1.1", "2PM2.3"
  description: text('description'),
  expectedLearning: jsonb('expected_learning'), // Array of expected learnings
  indicators: jsonb('indicators'), // Array of evaluation indicators
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Official Projects Structure (Proyectos Sociocríticos)
export const sepProjects = pgTable('sep_projects', {
  id: text('id').primaryKey(),
  name: text('name').notNull(), // "Proyecto de Aula: Mi Comunidad"
  grade: text('grade').notNull(),
  subject: text('subject').notNull(),
  type: text('type').notNull(), // "proyecto_aula", "proyecto_comunitario"
  duration: text('duration'), // "4 semanas", "bimestre"
  description: text('description'),
  objectives: jsonb('objectives'), // Array of project objectives
  phases: jsonb('phases'), // Array of project phases
  resources: jsonb('resources'), // Required resources
  evaluationCriteria: jsonb('evaluation_criteria'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Official Normative Documents
export const sepNormativeDocuments = pgTable('sep_normative_documents', {
  id: text('id').primaryKey(),
  title: text('title').notNull(), // "Acuerdo 11/06/18"
  documentType: text('document_type').notNull(), // "acuerdo", "circular", "oficio"
  number: text('number'), // "11/06/18"
  date: timestamp('date'), // Publication date
  description: text('description'),
  content: jsonb('content'), // Document content structure
  url: text('url'), // Official URL
  category: text('category'), // "curriculum", "evaluation", "inclusion"
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Official Templates and Examples
export const sepOfficialTemplates = pgTable('sep_official_templates', {
  id: text('id').primaryKey(),
  name: text('name').notNull(), // "Plantilla de Planeación NEM"
  type: text('type').notNull(), // "planeacion", "actividad", "proyecto"
  grade: text('grade'),
  subject: text('subject'),
  description: text('description'),
  structure: jsonb('structure'), // Template structure
  example: jsonb('example'), // Example filled template
  guidelines: jsonb('guidelines'), // Usage guidelines
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Relationships between PDAs and Projects
export const sepPdaProjectRelations = pgTable('sep_pda_project_relations', {
  pdaId: text('pda_id').references(() => sepLearningPurposes.id),
  projectId: text('project_id').references(() => sepProjects.id),
}, (table) => ({
  pk: primaryKey({ columns: [table.pdaId, table.projectId] }),
}));

// Official Assessment Instruments
export const sepAssessmentInstruments = pgTable('sep_assessment_instruments', {
  id: text('id').primaryKey(),
  name: text('name').notNull(), // "Rúbrica de Evaluación Formativa"
  type: text('type').notNull(), // "rubrica", "lista_cotejo", "escala"
  grade: text('grade'),
  subject: text('subject'),
  description: text('description'),
  structure: jsonb('structure'), // Instrument structure
  criteria: jsonb('criteria'), // Evaluation criteria
  examples: jsonb('examples'), // Usage examples
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});