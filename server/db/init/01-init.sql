-- Initialize database for Planeación NEM
-- This script runs automatically when the PostgreSQL container starts

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS public;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE planeacion_nem TO nem_user;
GRANT ALL PRIVILEGES ON SCHEMA public TO nem_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO nem_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO nem_user;

-- Set timezone
SET timezone = 'America/Mexico_City';