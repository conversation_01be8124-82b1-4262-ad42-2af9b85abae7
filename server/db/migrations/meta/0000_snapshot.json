{"id": "5d7578c9-819e-4a92-b1b3-e2461a5ba00a", "prevId": "00000000-0000-0000-0000-000000000000", "version": "5", "dialect": "pg", "tables": {"chat_messages": {"name": "chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "'gen_random_uuid()'"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "planning_id": {"name": "planning_id", "type": "uuid", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "context": {"name": "context", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_messages_user_id_users_id_fk": {"name": "chat_messages_user_id_users_id_fk", "tableFrom": "chat_messages", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_messages_planning_id_plannings_id_fk": {"name": "chat_messages_planning_id_plannings_id_fk", "tableFrom": "chat_messages", "tableTo": "plannings", "columnsFrom": ["planning_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "nem_resources": {"name": "nem_resources", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "'gen_random_uuid()'"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(300)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": true}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_official": {"name": "is_official", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "downloads": {"name": "downloads", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "plannings": {"name": "plannings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "'gen_random_uuid()'"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(300)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "formative_field": {"name": "formative_field", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "articulating_axes": {"name": "articulating_axes", "type": "jsonb", "primaryKey": false, "notNull": true}, "learning_process": {"name": "learning_process", "type": "jsonb", "primaryKey": false, "notNull": true}, "activities": {"name": "activities", "type": "jsonb", "primaryKey": false, "notNull": true}, "evaluation": {"name": "evaluation", "type": "jsonb", "primaryKey": false, "notNull": true}, "resources": {"name": "resources", "type": "jsonb", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'draft'"}, "coherence_score": {"name": "coherence_score", "type": "integer", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"plannings_user_id_users_id_fk": {"name": "plannings_user_id_users_id_fk", "tableFrom": "plannings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "sessions": {"name": "sessions", "schema": "", "columns": {"sid": {"name": "sid", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "templates": {"name": "templates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "'gen_random_uuid()'"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "formative_field": {"name": "formative_field", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "template": {"name": "template", "type": "jsonb", "primaryKey": false, "notNull": true}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "usage": {"name": "usage", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"templates_user_id_users_id_fk": {"name": "templates_user_id_users_id_fk", "tableFrom": "templates", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "'gen_random_uuid()'"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "school": {"name": "school", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "zone": {"name": "zone", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "sector": {"name": "sector", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}