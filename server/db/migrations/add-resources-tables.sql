-- Migration to add resource ratings, favorites, and downloads tables

-- Create resource_ratings table
CREATE TABLE IF NOT EXISTS resource_ratings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) NOT NULL,
  resource_id UUID REFERENCES nem_resources(id) NOT NULL,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  UNIQUE(user_id, resource_id)
);

-- Create resource_favorites table
CREATE TABLE IF NOT EXISTS resource_favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) NOT NULL,
  resource_id UUID REFERENCES nem_resources(id) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  UNIQUE(user_id, resource_id)
);

-- Create resource_downloads table
CREATE TABLE IF NOT EXISTS resource_downloads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  resource_id UUID REFERENCES nem_resources(id) NOT NULL,
  downloaded_at TIMESTAMP DEFAULT NOW() NOT NULL,
  ip_address VARCHAR(45)
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_resource_ratings_resource_id ON resource_ratings(resource_id);
CREATE INDEX IF NOT EXISTS idx_resource_ratings_user_id ON resource_ratings(user_id);
CREATE INDEX IF NOT EXISTS idx_resource_favorites_resource_id ON resource_favorites(resource_id);
CREATE INDEX IF NOT EXISTS idx_resource_favorites_user_id ON resource_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_resource_downloads_resource_id ON resource_downloads(resource_id);
CREATE INDEX IF NOT EXISTS idx_resource_downloads_user_id ON resource_downloads(user_id);

-- Insert sample data for testing
INSERT INTO nem_resources (title, type, category, grade, subject, content, tags, is_official, downloads, rating) VALUES
  ('PDA: Participa en intercambios orales mediante la formulación de preguntas', 'pda', 'Lenguajes', '1° Secundaria', 'Español', '{"description": "Proceso de desarrollo de aprendizaje oficial para el campo formativo de Lenguajes", "objectives": ["Desarrollar habilidades de comunicación oral", "Formular preguntas pertinentes"], "indicators": ["Participa activamente", "Formula preguntas coherentes"]}', '["oficial", "comunicación", "oralidad"]', true, 1250, 95),
  ('Actividad: Debate sobre Cambio Climático', 'activity', 'Ética, Naturaleza y Sociedades', '2° Secundaria', 'Geografía', '{"description": "Actividad socioconstructivista para desarrollar pensamiento crítico sobre el cambio climático", "duration": 90, "materials": ["Artículos científicos", "Presentaciones", "Rúbrica de debate"], "steps": ["Investigación previa", "Formación de equipos", "Debate estructurado", "Reflexión final"]}', '["debate", "cambio climático", "pensamiento crítico"]', false, 890, 88),
  ('Proyecto Comunitario: Huerto Escolar Sustentable', 'project', 'Ética, Naturaleza y Sociedades', '3° Secundaria', 'Ciencias Naturales', '{"description": "Proyecto de aula, escuela y comunidad para desarrollar conciencia ambiental", "phases": ["Planificación", "Implementación", "Mantenimiento", "Evaluación"], "community_impact": "Mejora del entorno escolar y conciencia ambiental", "duration_weeks": 12}', '["sustentabilidad", "comunidad", "medio ambiente"]', true, 2100, 92),
  ('Rúbrica de Evaluación: Expresión Oral', 'rubric', 'Lenguajes', '1° Secundaria', 'Español', '{"description": "Rúbrica analítica para evaluar competencias de expresión oral", "criteria": ["Claridad", "Coherencia", "Vocabulario", "Fluidez", "Interacción"], "levels": ["Excelente", "Satisfactorio", "En desarrollo", "Requiere apoyo"]}', '["evaluación", "expresión oral", "rúbrica"]', true, 1680, 90),
  ('Documento: Marco Curricular NEM 2024', 'document', 'Todos', null, null, '{"description": "Documento oficial con el marco curricular completo de la Nueva Escuela Mexicana", "pages": 156, "sections": ["Campos Formativos", "Ejes Articuladores", "PDAs", "Metodología"]}', '["marco curricular", "nem", "oficial"]', true, 5420, 98);