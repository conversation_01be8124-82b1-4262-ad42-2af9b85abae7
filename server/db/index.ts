import { drizzle } from 'drizzle-orm/node-postgres';
import pg from 'pg';
import * as schema from './schema.js';

const { Pool } = pg;

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5434'),
  database: process.env.DB_NAME || 'planeacion_nem_dev',
  user: process.env.DB_USER || 'nem_dev',
  password: process.env.DB_PASSWORD || 'nem_dev_password'
});

export const db = drizzle(pool, { schema });
export { schema };