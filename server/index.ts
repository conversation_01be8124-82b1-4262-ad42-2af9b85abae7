import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import session from "express-session";
import { createServer } from "http";
import { authRoutes } from "./routes/auth.js";
import { planningRoutes } from "./routes/planning.js";
import { resourceRoutes } from "./routes/resources.js";
import { aiRoutes } from "./routes/ai.js";
import sepRoutes from "./routes/sep.js";
import sepCurriculumRoutes from "./routes/sep-curriculum.js";
import { templateRoutes } from "./routes/templates.js";

dotenv.config();

const app = express();
const server = createServer(app);

// Session configuration
app.use(
  session({
    secret: process.env.SESSION_SECRET || "fallback-secret-key",
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === "production",
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
  })
);

// Middleware
app.use(
  cors({
    origin: [
      process.env.CLIENT_URL || "http://localhost:5173",
      "http://localhost:5174",
      "http://localhost:3000",
    ],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
app.use(express.json());

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/planning", planningRoutes);
app.use("/api/resources", resourceRoutes);
app.use("/api/ai", aiRoutes);
app.use("/api/sep", sepRoutes);
app.use("/api/sep-curriculum", sepCurriculumRoutes);
app.use("/api/templates", templateRoutes);

// Health check endpoint
app.get("/health", (_req, res) => {
  res.json({ status: "ok", timestamp: new Date().toISOString() });
});

// Root endpoint
app.get("/", (_req, res) => {
  res.json({
    message: "Planeación NEM API",
    version: "1.0.0",
    endpoints: {
      auth: "/api/auth",
      planning: "/api/planning",
      resources: "/api/resources",
      ai: "/api/ai",
      sep: "/api/sep",
      "sep-curriculum": "/api/sep-curriculum",
      templates: "/api/templates",
    },
  });
});

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📚 Environment: ${process.env.NODE_ENV || "development"}`);
});
