import { Router } from 'express';
import { db } from '../db/index.js';
import { nemResources, resourceRatings, resourceFavorites } from '../db/schema.js';
import { eq, ilike, desc, and } from 'drizzle-orm';
import { requireAuth } from '../middleware/auth.js';

const router = Router();

// Get NEM resources with filters
router.get('/', async (req, res) => {
  try {
    const { type, category, grade, subject, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    
    let resources;
    
    if (type) {
      resources = await db.select().from(nemResources).where(eq(nemResources.type, type as string));
    } else if (category) {
      resources = await db.select().from(nemResources).where(eq(nemResources.category, category as string));
    } else if (grade) {
      resources = await db.select().from(nemResources).where(eq(nemResources.grade, grade as string));
    } else if (subject) {
      resources = await db.select().from(nemResources).where(eq(nemResources.subject, subject as string));
    } else if (search) {
      resources = await db.select().from(nemResources).where(ilike(nemResources.title, `%${search}%`));
    } else {
      resources = await db.select().from(nemResources);
    }
    
    // Calculate ratings for each resource
    const resourcesWithRatings = await Promise.all(
      resources.map(async (resource) => {
        const ratings = await db.select().from(resourceRatings)
          .where(eq(resourceRatings.resourceId, resource.id));
        
        const averageRating = ratings.length > 0 
          ? Math.round(ratings.reduce((sum, r) => sum + (r.rating || 0), 0) / ratings.length)
          : 0;
        
        return {
          ...resource,
          averageRating,
          ratingCount: ratings.length
        };
      })
    );
    
    // Apply sorting
    if (sortBy === 'downloads') {
      resourcesWithRatings.sort((a, b) => 
        sortOrder === 'desc' ? b.downloads - a.downloads : a.downloads - b.downloads
      );
    } else if (sortBy === 'rating') {
      resourcesWithRatings.sort((a, b) => 
        sortOrder === 'desc' ? b.averageRating - a.averageRating : a.averageRating - b.averageRating
      );
    } else {
      resourcesWithRatings.sort((a, b) => 
        sortOrder === 'desc' 
          ? new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          : new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );
    }
    
    res.json({ resources: resourcesWithRatings });
  } catch (error) {
    console.error('Get resources error:', error);
    res.status(500).json({ error: 'Error al obtener recursos' });
  }
});

// Get single resource
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const [resource] = await db.select().from(nemResources).where(eq(nemResources.id, id));
    
    if (!resource) {
      return res.status(404).json({ error: 'Recurso no encontrado' });
    }
    
    // Get ratings
    const ratings = await db.select().from(resourceRatings)
      .where(eq(resourceRatings.resourceId, id));
    
    const averageRating = ratings.length > 0 
      ? Math.round(ratings.reduce((sum, r) => sum + (r.rating || 0), 0) / ratings.length)
      : 0;
    
    res.json({ 
      resource: {
        ...resource,
        averageRating,
        ratingCount: ratings.length
      }
    });
  } catch (error) {
    console.error('Get resource error:', error);
    res.status(500).json({ error: 'Error al obtener recurso' });
  }
});

// Get user's favorite resources
router.get('/favorites', requireAuth, async (req, res) => {
  try {
    const userId = req.session.userId;
    
    const favorites = await db.select().from(resourceFavorites)
      .where(eq(resourceFavorites.userId, userId!));
    
    const favoriteResources = await Promise.all(
      favorites.map(async (fav) => {
        const [resource] = await db.select().from(nemResources)
          .where(eq(nemResources.id, fav.resourceId));
        
        if (!resource) return null;
        
        const ratings = await db.select().from(resourceRatings)
          .where(eq(resourceRatings.resourceId, resource.id));
        
        const averageRating = ratings.length > 0 
          ? Math.round(ratings.reduce((sum, r) => sum + (r.rating || 0), 0) / ratings.length)
          : 0;
        
        return {
          ...resource,
          averageRating,
          ratingCount: ratings.length
        };
      })
    );
    
    const validResources = favoriteResources.filter(Boolean);
    
    res.json({ resources: validResources });
  } catch (error) {
    console.error('Get favorites error:', error);
    res.status(500).json({ error: 'Error al obtener favoritos' });
  }
});

// Toggle favorite
router.post('/:id/favorite', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.session.userId;
    
    // Check if already favorited
    const existing = await db.select().from(resourceFavorites)
      .where(
        and(
          eq(resourceFavorites.userId, userId!),
          eq(resourceFavorites.resourceId, id)
        )
      );
    
    if (existing.length > 0) {
      // Remove from favorites
      await db.delete(resourceFavorites)
        .where(
          and(
            eq(resourceFavorites.userId, userId!),
            eq(resourceFavorites.resourceId, id)
          )
        );
      
      res.json({ favorited: false });
    } else {
      // Add to favorites
      await db.insert(resourceFavorites).values({
        userId: userId!,
        resourceId: id
      });
      
      res.json({ favorited: true });
    }
  } catch (error) {
    console.error('Toggle favorite error:', error);
    res.status(500).json({ error: 'Error al actualizar favoritos' });
  }
});

// Rate resource
router.post('/:id/rate', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { rating } = req.body;
    const userId = req.session.userId;
    
    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({ error: 'Rating must be between 1 and 5' });
    }
    
    // Check if already rated
    const existing = await db.select().from(resourceRatings)
      .where(
        and(
          eq(resourceRatings.userId, userId!),
          eq(resourceRatings.resourceId, id)
        )
      );
    
    if (existing.length > 0) {
      // Update rating
      await db.update(resourceRatings)
        .set({ rating })
        .where(
          and(
            eq(resourceRatings.userId, userId!),
            eq(resourceRatings.resourceId, id)
          )
        );
    } else {
      // Add rating
      await db.insert(resourceRatings).values({
        userId: userId!,
        resourceId: id,
        rating
      });
    }
    
    res.json({ success: true });
  } catch (error) {
    console.error('Rate resource error:', error);
    res.status(500).json({ error: 'Error al calificar recurso' });
  }
});

// Track download
router.post('/:id/download', async (req, res) => {
  try {
    const { id } = req.params;
    
    const [resource] = await db.select().from(nemResources).where(eq(nemResources.id, id));
    
    if (!resource) {
      return res.status(404).json({ error: 'Recurso no encontrado' });
    }
    
    await db.update(nemResources)
      .set({ downloads: resource.downloads + 1 })
      .where(eq(nemResources.id, id));
    
    res.json({ success: true, downloads: resource.downloads + 1 });
  } catch (error) {
    console.error('Download tracking error:', error);
    res.status(500).json({ error: 'Error al registrar descarga' });
  }
});

// Get featured resources
router.get('/featured/popular', async (_req, res) => {
  try {
    const resources = await db.select().from(nemResources)
      .orderBy(desc(nemResources.downloads))
      .limit(10);

    const resourcesWithRatings = await Promise.all(
      resources.map(async (resource) => {
        const ratings = await db.select().from(resourceRatings)
          .where(eq(resourceRatings.resourceId, resource.id));
        
        const averageRating = ratings.length > 0 
          ? Math.round(ratings.reduce((sum, r) => sum + (r.rating || 0), 0) / ratings.length)
          : 0;
        
        return {
          ...resource,
          averageRating,
          ratingCount: ratings.length
        };
      })
    );

    res.json({ resources: resourcesWithRatings });
  } catch (error) {
    console.error('Get featured resources error:', error);
    res.status(500).json({ error: 'Error al obtener recursos destacados' });
  }
});

// Upload new resource
router.post('/upload', requireAuth, async (req, res) => {
  try {
    const { title, type, category, grade, subject, content, tags } = req.body;
    const userId = req.session.userId;

    if (!userId) {
      return res.status(401).json({ error: 'Usuario no autenticado' });
    }

    if (!title || !type || !category || !content) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const [newResource] = await db.insert(nemResources).values({
      title,
      type,
      category,
      grade,
      subject,
      content,
      tags: tags || [],
      isOfficial: false
    }).returning();

    res.json({ resource: newResource });
  } catch (error) {
    console.error('Upload resource error', error);
    res.status(500).json({ error: 'Error al subir recurso' });
  }
});

export { router as resourceRoutes };