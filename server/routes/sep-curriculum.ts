import { Router } from 'express';
import { db } from '../db/index.js';
import { 
  sepCurriculumFrameworks, 
  sepFormativeFields, 
  sepArticulatingAxes, 
  sepLearningPurposes,
  sepProjects,
  sepNormativeDocuments,
  sepOfficialTemplates,
  sepAssessmentInstruments
} from '../db/schema-sep-curriculum.js';
import { eq, and, ilike, desc } from 'drizzle-orm';

const router = Router();

// Get all curriculum frameworks
router.get('/frameworks', async (req, res) => {
  try {
    const frameworks = await db.select().from(sepCurriculumFrameworks)
      .where(eq(sepCurriculumFrameworks.isActive, true))
      .orderBy(desc(sepCurriculumFrameworks.createdAt));

    res.json({
      success: true,
      data: frameworks
    });
  } catch (error) {
    console.error('Get frameworks error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener marcos curriculares'
    });
  }
});

// Get formative fields by framework
router.get('/frameworks/:frameworkId/fields', async (req, res) => {
  try {
    const { frameworkId } = req.params;
    
    const fields = await db.select().from(sepFormativeFields)
      .where(and(
        eq(sepFormativeFields.frameworkId, frameworkId),
        eq(sepFormativeFields.isActive, true)
      ))
      .orderBy(sepFormativeFields.order);

    res.json({
      success: true,
      data: fields
    });
  } catch (error) {
    console.error('Get fields error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener campos formativos'
    });
  }
});

// Get articulating axes by field
router.get('/fields/:fieldId/axes', async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { grade } = req.query;
    
    let whereClause = and(
      eq(sepArticulatingAxes.fieldId, fieldId),
      eq(sepArticulatingAxes.isActive, true)
    );
    
    if (grade) {
      whereClause = and(whereClause, eq(sepArticulatingAxes.grade, grade as string));
    }

    const axes = await db.select().from(sepArticulatingAxes)
      .where(whereClause)
      .orderBy(sepArticulatingAxes.order);

    res.json({
      success: true,
      data: axes
    });
  } catch (error) {
    console.error('Get axes error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener ejes articuladores'
    });
  }
});

// Get learning purposes (PDAs) with filtering
router.get('/learning-purposes', async (req, res) => {
  try {
    const { grade, subject, field, axis } = req.query;
    
    const conditions = [eq(sepLearningPurposes.isActive, true)];
    
    if (grade) {
      conditions.push(eq(sepLearningPurposes.grade, grade as string));
    }
    if (subject) {
      conditions.push(ilike(sepLearningPurposes.subject, subject as string));
    }
    if (field) {
      conditions.push(eq(sepLearningPurposes.fieldId, field as string));
    }
    if (axis) {
      conditions.push(eq(sepLearningPurposes.axisId, axis as string));
    }

    const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0];

    const purposes = await db.select({
      id: sepLearningPurposes.id,
      grade: sepLearningPurposes.grade,
      subject: sepLearningPurposes.subject,
      purpose: sepLearningPurposes.purpose,
      code: sepLearningPurposes.code,
      description: sepLearningPurposes.description,
      expectedLearning: sepLearningPurposes.expectedLearning,
      indicators: sepLearningPurposes.indicators,
      field: {
        id: sepFormativeFields.id,
        name: sepFormativeFields.name,
        code: sepFormativeFields.code
      },
      axis: {
        id: sepArticulatingAxes.id,
        name: sepArticulatingAxes.name,
        code: sepArticulatingAxes.code
      }
    })
    .from(sepLearningPurposes)
    .leftJoin(sepFormativeFields, eq(sepLearningPurposes.fieldId, sepFormativeFields.id))
    .leftJoin(sepArticulatingAxes, eq(sepLearningPurposes.axisId, sepArticulatingAxes.id))
    .where(whereClause)
    .orderBy(sepLearningPurposes.code);

    res.json({
      success: true,
      data: purposes
    });
  } catch (error) {
    console.error('Get learning purposes error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener propósitos de aprendizaje'
    });
  }
});

// Get projects with filtering
router.get('/projects', async (req, res) => {
  try {
    const { grade, subject, type } = req.query;
    
    const conditions = [eq(sepProjects.isActive, true)];
    
    if (grade) {
      conditions.push(eq(sepProjects.grade, grade as string));
    }
    if (subject) {
      conditions.push(eq(sepProjects.subject, subject as string));
    }
    if (type) {
      conditions.push(eq(sepProjects.type, type as string));
    }

    const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0];

    const projects = await db.select().from(sepProjects)
      .where(whereClause)
      .orderBy(sepProjects.grade, sepProjects.name);

    res.json({
      success: true,
      data: projects
    });
  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener proyectos'
    });
  }
});

// Get normative documents
router.get('/normative-documents', async (req, res) => {
  try {
    const { category, type } = req.query;
    
    const conditions = [eq(sepNormativeDocuments.isActive, true)];
    
    if (category) {
      conditions.push(eq(sepNormativeDocuments.category, category as string));
    }
    if (type) {
      conditions.push(eq(sepNormativeDocuments.documentType, type as string));
    }

    const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0];

    const documents = await db.select().from(sepNormativeDocuments)
      .where(whereClause)
      .orderBy(desc(sepNormativeDocuments.date));

    res.json({
      success: true,
      data: documents
    });
  } catch (error) {
    console.error('Get normative documents error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener documentos normativos'
    });
  }
});

// Get official templates
router.get('/templates', async (req, res) => {
  try {
    const { type, grade, subject } = req.query;
    
    const conditions = [eq(sepOfficialTemplates.isActive, true)];
    
    if (type) {
      conditions.push(eq(sepOfficialTemplates.type, type as string));
    }
    if (grade) {
      conditions.push(eq(sepOfficialTemplates.grade, grade as string));
    }
    if (subject) {
      conditions.push(eq(sepOfficialTemplates.subject, subject as string));
    }

    const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0];

    const templates = await db.select().from(sepOfficialTemplates)
      .where(whereClause)
      .orderBy(sepOfficialTemplates.name);

    res.json({
      success: true,
      data: templates
    });
  } catch (error) {
    console.error('Get templates error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener plantillas oficiales'
    });
  }
});

// Get assessment instruments
router.get('/assessment-instruments', async (req, res) => {
  try {
    const { type, grade, subject } = req.query;
    
    const conditions = [eq(sepAssessmentInstruments.isActive, true)];
    
    if (type) {
      conditions.push(eq(sepAssessmentInstruments.type, type as string));
    }
    if (grade) {
      conditions.push(eq(sepAssessmentInstruments.grade, grade as string));
    }
    if (subject) {
      conditions.push(eq(sepAssessmentInstruments.subject, subject as string));
    }

    const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0];

    const instruments = await db.select().from(sepAssessmentInstruments)
      .where(whereClause)
      .orderBy(sepAssessmentInstruments.name);

    res.json({
      success: true,
      data: instruments
    });
  } catch (error) {
    console.error('Get assessment instruments error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener instrumentos de evaluación'
    });
  }
});

// Get complete curriculum structure
router.get('/curriculum-structure', async (req, res) => {
  try {
    const { grade, subject } = req.query;
    
    // Get active framework
    const frameworks = await db.select().from(sepCurriculumFrameworks)
      .where(eq(sepCurriculumFrameworks.isActive, true))
      .limit(1);
    
    if (frameworks.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No hay marcos curriculares activos'
      });
    }

    const framework = frameworks[0];
    
    // Get fields for this framework
    const fields = await db.select().from(sepFormativeFields)
      .where(and(
        eq(sepFormativeFields.frameworkId, framework.id),
        eq(sepFormativeFields.isActive, true)
      ))
      .orderBy(sepFormativeFields.order);

    // Get axes for each field
    const fieldsWithAxes = await Promise.all(
      fields.map(async (field) => {
        const conditions = [
          eq(sepArticulatingAxes.fieldId, field.id),
          eq(sepArticulatingAxes.isActive, true)
        ];
        if (grade) {
          conditions.push(eq(sepArticulatingAxes.grade, grade as string));
        }

        const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0];

        const axes = await db.select().from(sepArticulatingAxes)
          .where(whereClause)
          .orderBy(sepArticulatingAxes.order);

        return {
          ...field,
          axes
        };
      })
    );

    // Get PDAs
    const conditions = [eq(sepLearningPurposes.isActive, true)];
    if (grade) {
      conditions.push(eq(sepLearningPurposes.grade, grade as string));
    }
    if (subject) {
      conditions.push(ilike(sepLearningPurposes.subject, subject as string));
    }

    const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0];

    const pdas = await db.select().from(sepLearningPurposes)
      .where(whereClause)
      .orderBy(sepLearningPurposes.code);

    res.json({
      success: true,
      data: {
        framework,
        fields: fieldsWithAxes,
        learningPurposes: pdas
      }
    });
  } catch (error) {
    console.error('Get curriculum structure error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener estructura curricular'
    });
  }
});

export default router;