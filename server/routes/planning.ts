import { Router } from 'express';
import { db } from '../db/index.js';
import { plannings } from '../db/schema.js';
import { eq, desc } from 'drizzle-orm';
import { z } from 'zod';
import { requireAuth } from '../middleware/auth.js';

const router = Router();

// Planning validation schema
const planningSchema = z.object({
  title: z.string().min(1),
  subject: z.string().min(1),
  grade: z.string().min(1),
  formativeField: z.string().min(1),
  articulatingAxes: z.array(z.string()),
  learningProcess: z.object({
    pda: z.string(),
    description: z.string(),
    objectives: z.array(z.string()),
    competencies: z.array(z.string()),
    indicators: z.array(z.string()),
    methodology: z.string(),
    resources: z.array(z.string()),
    evaluation: z.object({
      type: z.string(),
      instruments: z.array(z.string()),
      criteria: z.array(z.string()),
    }),
  }),
  activities: z.array(z.object({
    title: z.string(),
    description: z.string(),
    duration: z.string(),
    materials: z.array(z.string()),
    methodology: z.string(),
  })),
  evaluation: z.object({
    type: z.string(),
    instruments: z.array(z.string()),
    criteria: z.array(z.string()),
  }),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  resources: z.array(z.string()).optional(),
});

// Get all plannings for current user
router.get('/', requireAuth, async (req, res) => {
  try {
    const userPlannings = await db
      .select()
      .from(plannings)
      .where(eq(plannings.userId, req.session.userId!))
      .orderBy(desc(plannings.createdAt));

    res.json({ plannings: userPlannings });
  } catch (error) {
    console.error('Get plannings error:', error);
    res.status(500).json({ error: 'Error al obtener planeaciones' });
  }
});

// Get single planning
router.get('/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    
    const [planning] = await db
      .select()
      .from(plannings)
      .where(eq(plannings.id, id));

    if (!planning || planning.userId !== req.session.userId) {
      return res.status(404).json({ error: 'Planeación no encontrada' });
    }

    res.json({ planning });
  } catch (error) {
    console.error('Get planning error:', error);
    res.status(500).json({ error: 'Error al obtener planeación' });
  }
});

// Create new planning
router.post('/', requireAuth, async (req, res) => {
  try {
    const validatedData = planningSchema.parse(req.body);
    
    const [newPlanning] = await db.insert(plannings).values({
      title: validatedData.title,
      subject: validatedData.subject,
      grade: validatedData.grade,
      formativeField: validatedData.formativeField,
      articulatingAxes: validatedData.articulatingAxes,
      learningProcess: validatedData.learningProcess,
      activities: validatedData.activities,
      evaluation: validatedData.evaluation,
      resources: validatedData.resources || [],
      startDate: validatedData.startDate ? new Date(validatedData.startDate) : new Date(),
      endDate: validatedData.endDate ? new Date(validatedData.endDate) : new Date(),
      userId: req.session.userId!,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    res.status(201).json({ planning: newPlanning });
  } catch (error) {
    console.error('Create planning error:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Datos inválidos', details: error.errors });
    }
    res.status(500).json({ error: 'Error al crear planeación' });
  }
});

// Update planning
router.put('/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const validatedData = planningSchema.partial().parse(req.body);

    const updateData: Record<string, any> = {
      updatedAt: new Date(),
    };

    if (validatedData.title !== undefined) updateData.title = validatedData.title;
    if (validatedData.subject !== undefined) updateData.subject = validatedData.subject;
    if (validatedData.grade !== undefined) updateData.grade = validatedData.grade;
    if (validatedData.formativeField !== undefined) updateData.formativeField = validatedData.formativeField;
    if (validatedData.articulatingAxes !== undefined) updateData.articulatingAxes = validatedData.articulatingAxes;
    if (validatedData.learningProcess !== undefined) updateData.learningProcess = validatedData.learningProcess;
    if (validatedData.activities !== undefined) updateData.activities = validatedData.activities;
    if (validatedData.evaluation !== undefined) updateData.evaluation = validatedData.evaluation;
    if (validatedData.resources !== undefined) updateData.resources = validatedData.resources;
    
    if (validatedData.startDate !== undefined) {
      updateData.startDate = new Date(validatedData.startDate);
    }
    if (validatedData.endDate !== undefined) {
      updateData.endDate = new Date(validatedData.endDate);
    }

    const [updatedPlanning] = await db.update(plannings)
      .set(updateData)
      .where(eq(plannings.id, id))
      .returning();

    if (!updatedPlanning) {
      return res.status(404).json({ error: 'Planeación no encontrada' });
    }

    res.json({ planning: updatedPlanning });
  } catch (error) {
    console.error('Update planning error:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Datos inválidos', details: error.errors });
    }
    res.status(500).json({ error: 'Error al actualizar planeación' });
  }
});

// Delete planning
router.delete('/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    
    const [deletedPlanning] = await db.delete(plannings)
      .where(eq(plannings.id, id))
      .returning({ id: plannings.id });

    if (!deletedPlanning) {
      return res.status(404).json({ error: 'Planeación no encontrada' });
    }

    res.json({ message: 'Planeación eliminada exitosamente' });
  } catch (error) {
    console.error('Delete planning error:', error);
    res.status(500).json({ error: 'Error al eliminar planeación' });
  }
});

export { router as planningRoutes };