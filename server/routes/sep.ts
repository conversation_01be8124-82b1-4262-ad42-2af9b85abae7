import { Router } from 'express';
import { db } from '../db/index.js';
import { nemResources } from '../db/schema.js';
import { eq, ilike, desc, sql } from 'drizzle-orm';

const router = Router();

// Get all SEP resources
router.get('/resources', async (req, res) => {
  try {
    const { grade, subject, type, search, limit = 50, offset = 0 } = req.query;

    let results;
    let totalCount;

    if (grade) {
      results = await db.select().from(nemResources)
        .where(eq(nemResources.grade, grade as string))
        .limit(Number(limit)).offset(Number(offset));
      const countResult = await db.select({ count: sql<number>`count(*)` }).from(nemResources)
        .where(eq(nemResources.grade, grade as string));
      totalCount = countResult[0]?.count || 0;
    } else if (subject) {
      results = await db.select().from(nemResources)
        .where(eq(nemResources.subject, subject as string))
        .limit(Number(limit)).offset(Number(offset));
      const countResult = await db.select({ count: sql<number>`count(*)` }).from(nemResources)
        .where(eq(nemResources.subject, subject as string));
      totalCount = countResult[0]?.count || 0;
    } else if (type) {
      results = await db.select().from(nemResources)
        .where(eq(nemResources.type, type as string))
        .limit(Number(limit)).offset(Number(offset));
      const countResult = await db.select({ count: sql<number>`count(*)` }).from(nemResources)
        .where(eq(nemResources.type, type as string));
      totalCount = countResult[0]?.count || 0;
    } else if (search) {
      results = await db.select().from(nemResources)
        .where(ilike(nemResources.title, `%${search}%`))
        .limit(Number(limit)).offset(Number(offset));
      const countResult = await db.select({ count: sql<number>`count(*)` }).from(nemResources)
        .where(ilike(nemResources.title, `%${search}%`));
      totalCount = countResult[0]?.count || 0;
    } else {
      results = await db.select().from(nemResources)
        .limit(Number(limit)).offset(Number(offset));
      const countResult = await db.select({ count: sql<number>`count(*)` }).from(nemResources);
      totalCount = countResult[0]?.count || 0;
    }

    res.json({
      success: true,
      data: {
        resources: results,
        total: totalCount,
        limit: Number(limit),
        offset: Number(offset)
      }
    });
  } catch (error) {
    console.error('Get SEP resources error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener recursos SEP'
    });
  }
});

// Get resource by ID
router.get('/resources/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const results = await db.select().from(nemResources).where(eq(nemResources.id, id));
    const resource = results[0];
    
    if (!resource) {
      return res.status(404).json({
        success: false,
        error: 'Recurso no encontrado'
      });
    }

    res.json({
      success: true,
      data: resource
    });
  } catch (error) {
    console.error('Get resource error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener recurso'
    });
  }
});

// Get resources by category
router.get('/resources/category/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const { limit = 20 } = req.query;
    
    const resources = await db.select().from(nemResources)
      .where(eq(nemResources.category, category))
      .limit(Number(limit));

    res.json({
      success: true,
      data: resources
    });
  } catch (error) {
    console.error('Get resources by category error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener recursos por categoría'
    });
  }
});

// Get resources by grade
router.get('/resources/grade/:grade', async (req, res) => {
  try {
    const { grade } = req.params;
    const { limit = 20 } = req.query;
    
    const resources = await db.select().from(nemResources)
      .where(eq(nemResources.grade, grade))
      .limit(Number(limit));

    res.json({
      success: true,
      data: resources
    });
  } catch (error) {
    console.error('Get resources by grade error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener recursos por grado'
    });
  }
});

// Get featured resources
router.get('/resources/featured', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const resources = await db.select().from(nemResources)
      .orderBy(desc(nemResources.downloads))
      .limit(Number(limit));

    res.json({
      success: true,
      data: resources
    });
  } catch (error) {
    console.error('Get featured resources error:', error);
    res.status(500).json({
      success: false,
      error: 'Error al obtener recursos destacados'
    });
  }
});

// Increment download count
router.post('/resources/:id/download', async (req, res) => {
  try {
    const { id } = req.params;
    
    await db.update(nemResources)
      .set({ 
        downloads: sql`${nemResources.downloads} + 1`,
        updatedAt: new Date()
      })
      .where(eq(nemResources.id, id));

    res.json({
      success: true,
      message: 'Descarga registrada'
    });
  } catch (error) {
    console.error('Error incrementing download count:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Error al registrar descarga' 
    });
  }
});

export default router;