import { Router } from "express";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { db } from "../db/index.js";
import { chatMessages } from "../db/schema.js";
import { requireAuth } from "../middleware/auth.js";
import { z } from "zod";

const router = Router();

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

// Validation schemas
const chatSchema = z.object({
  message: z.string().min(1),
  context: z
    .object({
      planningId: z.string().optional(),
      step: z.number().optional(),
      formativeField: z.string().optional(),
      subject: z.string().optional(),
      grade: z.string().optional(),
    })
    .optional(),
});

const generateContentSchema = z.object({
  type: z.enum(["activities", "evaluation", "objectives", "projects"]),
  context: z.object({
    formativeField: z.string(),
    grade: z.string(),
    subject: z.string(),
    pda: z.string().optional(),
    articulatingAxes: z.array(z.string()).optional(),
  }),
});

// System prompts for different contexts
const SYSTEM_PROMPTS = {
  general: `Eres un asistente pedagógico especializado en la Nueva Escuela Mexicana (NEM). 
    Tu función es apoyar a los docentes de secundaria en la creación de planeaciones didácticas 
    alineadas con los 4 Campos Formativos y los 7 Ejes Articuladores de la NEM.
    
    Campos Formativos:
    1. Lenguajes
    2. Saberes y Pensamiento Científico
    3. Ética, Naturaleza y Sociedades
    4. De lo Humano y lo Comunitario
    
    Ejes Articuladores:
    1. Inclusión
    2. Pensamiento Crítico
    3. Interculturalidad Crítica
    4. Igualdad de Género
    5. Vida Saludable
    6. Apropiación de las Culturas a través de la Lectura y la Escritura
    7. Artes y Experiencias Estéticas
    
    Siempre mantén un enfoque socioconstructivista, inclusivo y centrado en el desarrollo integral del estudiante.`,

  activities: `Genera actividades didácticas específicas para la Nueva Escuela Mexicana. 
    Las actividades deben ser:
    - Socioconstructivistas
    - Inclusivas y accesibles
    - Articuladas con los Ejes transversales
    - Orientadas al desarrollo de proyectos comunitarios
    - Contextualizadas culturalmente para México`,

  evaluation: `Diseña instrumentos de evaluación formativa alineados con la NEM.
    Incluye rúbricas, listas de cotejo y portafolios que evalúen:
    - Proceso de aprendizaje, no solo resultados
    - Competencias socioemocionales
    - Trabajo colaborativo
    - Pensamiento crítico
    - Aplicación práctica del conocimiento`,

  objectives: `Genera objetivos de aprendizaje específicos para la Nueva Escuela Mexicana.
    Los objetivos deben ser:
    - Claros y medibles
    - Alineados con el Campo Formativo correspondiente
    - Orientados al desarrollo integral del estudiante
    - Vinculados con los Ejes Articuladores
    - Contextualizados para la realidad mexicana
    - Enfocados en competencias para la vida`,

  projects: `Diseña proyectos educativos integrales para la Nueva Escuela Mexicana.
    Los proyectos deben ser:
    - Interdisciplinarios y transversales
    - Conectados con la comunidad local
    - Orientados a resolver problemas reales
    - Inclusivos y participativos
    - Culturalmente pertinentes
    - Promotores del trabajo colaborativo`,
};

// Chat with AI assistant
router.post("/chat", requireAuth, async (req, res) => {
  let message: string;
  let context: z.infer<typeof chatSchema>["context"];

  try {
    const parsed = chatSchema.parse(req.body);
    message = parsed.message;
    context = parsed.context;

    // Save user message
    await db.insert(chatMessages).values({
      userId: req.session.userId!,
      planningId: context?.planningId,
      role: "user",
      content: message,
      context,
    });

    // Build context for AI
    let systemPrompt = SYSTEM_PROMPTS.general;
    if (context) {
      systemPrompt += `\n\nContexto actual:
        - Campo Formativo: ${context.formativeField || "No especificado"}
        - Grado: ${context.grade || "No especificado"}
        - Materia: ${context.subject || "No especificado"}
        - Paso del wizard: ${context.step || "No especificado"}`;
    }

    // Generate AI response
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    const chat = model.startChat({
      history: [],
      generationConfig: {
        maxOutputTokens: 2048,
        temperature: 0.7,
      },
    });

    const result = await chat.sendMessage(
      `${systemPrompt}\n\nPregunta del docente: ${message}`
    );
    const aiResponse = result.response.text();

    // Save AI response
    await db.insert(chatMessages).values({
      userId: req.session.userId!,
      planningId: context?.planningId,
      role: "assistant",
      content: aiResponse,
      context,
    });

    res.json({ response: aiResponse });
  } catch (error) {
    console.error("Chat error:", error);
    if (error instanceof z.ZodError) {
      return res
        .status(400)
        .json({ error: "Datos inválidos", details: error.errors });
    }

    // Handle Google API errors
    if (error?.status === 503) {
      // Provide a fallback response when Google's service is overloaded
      const fallbackResponse = `Lo siento, el servicio de IA está temporalmente sobrecargado.

Como asistente pedagógico de la Nueva Escuela Mexicana, puedo sugerirte algunas ideas generales:

**Para tu planeación de ${context?.subject || "tu materia"} en ${
        context?.grade || "tu grado"
      }:**

• **Campo Formativo**: ${
        context?.formativeField ||
        "Selecciona el campo que mejor se alinee con tu materia"
      }
• **Ejes Articuladores**: Considera integrar Pensamiento Crítico e Inclusión como ejes principales
• **Metodología**: Utiliza estrategias socioconstructivistas que promuevan el aprendizaje colaborativo

**Actividades sugeridas:**
- Trabajo en equipos pequeños
- Resolución de problemas del contexto local
- Proyectos que conecten con la comunidad
- Uso de materiales del entorno

Por favor, intenta nuevamente en unos minutos cuando el servicio se haya estabilizado.`;

      // Save fallback response
      await db.insert(chatMessages).values({
        userId: req.session.userId!,
        planningId: context?.planningId,
        role: "assistant",
        content: fallbackResponse,
        context,
      });

      return res.json({ response: fallbackResponse });
    }

    if (error?.status === 401 || error?.status === 403) {
      return res.status(401).json({
        error: "Error de configuración de IA",
        message: "La API key de Gemini no está configurada correctamente.",
        type: "api_key_error",
      });
    }

    res.status(500).json({
      error: "Error en el chat con IA",
      message:
        "Ocurrió un error al procesar tu solicitud. Por favor, intenta de nuevo.",
      type: "general_error",
    });
  }
});

// Generate educational content
router.post("/generate", requireAuth, async (req, res) => {
  try {
    const { type, context } = generateContentSchema.parse(req.body);

    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });

    let prompt = "";

    switch (type) {
      case "activities":
        prompt = `${SYSTEM_PROMPTS.activities}
          
          Genera 3-5 actividades didácticas para:
          - Campo Formativo: ${context.formativeField}
          - Grado: ${context.grade}
          - Materia: ${context.subject}
          ${context.pda ? `- PDA: ${context.pda}` : ""}
          ${
            context.articulatingAxes
              ? `- Ejes Articuladores: ${context.articulatingAxes.join(", ")}`
              : ""
          }
          
          Para cada actividad incluye:
          1. Nombre de la actividad
          2. Descripción detallada
          3. Duración estimada
          4. Materiales necesarios
          5. Tipo de agrupamiento
          6. Vinculación con ejes articuladores
          
          Formato: JSON con array de actividades`;
        break;

      case "evaluation":
        prompt = `${SYSTEM_PROMPTS.evaluation}
          
          Diseña instrumentos de evaluación para:
          - Campo Formativo: ${context.formativeField}
          - Grado: ${context.grade}
          - Materia: ${context.subject}
          
          Incluye:
          1. Rúbrica de evaluación con criterios específicos
          2. Lista de cotejo para seguimiento
          3. Evidencias de aprendizaje sugeridas
          4. Estrategias de autoevaluación y coevaluación
          
          Formato: JSON estructurado`;
        break;

      case "objectives":
        prompt = `${SYSTEM_PROMPTS.objectives}

          Genera objetivos de aprendizaje para:
          - Campo Formativo: ${context.formativeField}
          - Grado: ${context.grade}
          - Materia: ${context.subject}
          ${context.pda ? `- PDA: ${context.pda}` : ""}
          ${
            context.articulatingAxes
              ? `- Ejes Articuladores: ${context.articulatingAxes.join(", ")}`
              : ""
          }

          Para cada objetivo incluye:
          1. Objetivo general del aprendizaje
          2. Objetivos específicos (3-5)
          3. Competencias a desarrollar
          4. Vinculación con ejes articuladores
          5. Indicadores de logro
          6. Criterios de evaluación

          Formato: JSON con estructura clara`;
        break;

      case "projects":
        prompt = `${SYSTEM_PROMPTS.projects}

          Diseña un proyecto educativo para:
          - Campo Formativo: ${context.formativeField}
          - Grado: ${context.grade}
          - Materia: ${context.subject}
          ${context.pda ? `- PDA: ${context.pda}` : ""}
          ${
            context.articulatingAxes
              ? `- Ejes Articuladores: ${context.articulatingAxes.join(", ")}`
              : ""
          }

          El proyecto debe incluir:
          1. Título y descripción general
          2. Objetivos del proyecto
          3. Fases de desarrollo (inicio, desarrollo, cierre)
          4. Actividades específicas por fase
          5. Recursos necesarios
          6. Cronograma sugerido
          7. Productos esperados
          8. Criterios de evaluación
          9. Vinculación comunitaria

          Formato: JSON estructurado`;
        break;

      default:
        throw new Error(`Tipo de contenido no soportado: ${type}`);
    }

    const result = await model.generateContent(prompt);
    const generatedContent = result.response.text();

    // Try to parse as JSON, fallback to plain text
    let parsedContent;
    try {
      parsedContent = JSON.parse(generatedContent);
    } catch {
      parsedContent = { content: generatedContent, type: "text" };
    }

    res.json({ content: parsedContent });
  } catch (error) {
    console.error("Generate content error:", error);
    if (error instanceof z.ZodError) {
      return res
        .status(400)
        .json({ error: "Datos inválidos", details: error.errors });
    }

    // Handle Google API errors
    if (error?.status === 503) {
      return res.status(503).json({
        error: "Servicio de IA temporalmente no disponible",
        message:
          "El servicio de Google está sobrecargado. Por favor, intenta de nuevo en unos minutos.",
        type: "service_unavailable",
      });
    }

    if (error?.status === 401 || error?.status === 403) {
      return res.status(401).json({
        error: "Error de configuración de IA",
        message: "La API key de Gemini no está configurada correctamente.",
        type: "api_key_error",
      });
    }

    res.status(500).json({
      error: "Error al generar contenido",
      message:
        "Ocurrió un error al procesar tu solicitud. Por favor, intenta de nuevo.",
      type: "general_error",
    });
  }
});

// Validate planning coherence
router.post("/validate", requireAuth, async (req, res) => {
  try {
    const planningData = req.body;

    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });

    const prompt = `Analiza la coherencia de esta planeación didáctica con los principios de la Nueva Escuela Mexicana:
    
    ${JSON.stringify(planningData, null, 2)}
    
    Evalúa:
    1. Alineación con el Campo Formativo
    2. Integración de Ejes Articuladores
    3. Coherencia entre objetivos, actividades y evaluación
    4. Enfoque socioconstructivista
    5. Inclusión y accesibilidad
    6. Pertinencia cultural
    
    Proporciona:
    - Puntuación de coherencia (0-100)
    - Fortalezas identificadas
    - Áreas de mejora
    - Sugerencias específicas
    
    Formato: JSON estructurado`;

    const result = await model.generateContent(prompt);
    const analysis = result.response.text();

    let parsedAnalysis;
    try {
      parsedAnalysis = JSON.parse(analysis);
    } catch {
      parsedAnalysis = {
        score: 0,
        analysis: analysis,
        type: "text",
      };
    }

    res.json({ validation: parsedAnalysis });
  } catch (error) {
    console.error("Validation error:", error);

    // Handle Google API errors
    if (error?.status === 503) {
      return res.status(503).json({
        error: "Servicio de IA temporalmente no disponible",
        message:
          "El servicio de Google está sobrecargado. Por favor, intenta de nuevo en unos minutos.",
        type: "service_unavailable",
      });
    }

    if (error?.status === 401 || error?.status === 403) {
      return res.status(401).json({
        error: "Error de configuración de IA",
        message: "La API key de Gemini no está configurada correctamente.",
        type: "api_key_error",
      });
    }

    res.status(500).json({
      error: "Error al validar planeación",
      message:
        "Ocurrió un error al procesar tu solicitud. Por favor, intenta de nuevo.",
      type: "general_error",
    });
  }
});

export { router as aiRoutes };
