import { Router } from 'express';
import { db } from '../db/index.js';
import { templates } from '../db/schema.js';
import { eq, or } from 'drizzle-orm';
import { requireAuth } from '../middleware/auth.js';
import { z } from 'zod';

const router = Router();

const templateSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  type: z.string().min(1),
  formativeField: z.string().optional(),
  grade: z.string().optional(),
  subject: z.string().optional(),
  template: z.object({}).passthrough(),
  isPublic: z.boolean().default(false),
});

// Get templates (user's + public)
router.get('/', requireAuth, async (req, res) => {
  try {
    const userTemplates = await db.select().from(templates)
      .where(or(
        eq(templates.userId, req.session.userId!),
        eq(templates.isPublic, true)
      ))
      .orderBy(templates.createdAt);

    res.json({ templates: userTemplates });
  } catch (error) {
    console.error('Get templates error:', error);
    res.status(500).json({ error: 'Error al obtener plantillas' });
  }
});

// Create template
router.post('/', requireAuth, async (req, res) => {
  try {
    const validatedData = templateSchema.parse(req.body);
    
    const [newTemplate] = await db.insert(templates).values({
      ...validatedData,
      userId: req.session.userId!,
    }).returning();

    res.status(201).json({ template: newTemplate });
  } catch (error) {
    console.error('Create template error:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Datos inválidos', details: error.errors });
    }
    res.status(500).json({ error: 'Error al crear plantilla' });
  }
});

// Update template
router.put('/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const validatedData = templateSchema.partial().parse(req.body);

    const [updatedTemplate] = await db.update(templates)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(eq(templates.id, id))
      .returning();

    if (!updatedTemplate) {
      return res.status(404).json({ error: 'Plantilla no encontrada' });
    }

    res.json({ template: updatedTemplate });
  } catch (error) {
    console.error('Update template error:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Datos inválidos', details: error.errors });
    }
    res.status(500).json({ error: 'Error al actualizar plantilla' });
  }
});

// Delete template
router.delete('/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const [deletedTemplate] = await db.delete(templates)
      .where(eq(templates.id, id))
      .returning({ id: templates.id });

    if (!deletedTemplate) {
      return res.status(404).json({ error: 'Plantilla no encontrada' });
    }

    res.json({ message: 'Plantilla eliminada exitosamente' });
  } catch (error) {
    console.error('Delete template error:', error);
    res.status(500).json({ error: 'Error al eliminar plantilla' });
  }
});

export { router as templateRoutes };